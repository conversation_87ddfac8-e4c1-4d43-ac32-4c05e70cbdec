<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <meta http-equiv='Content-Type' content='text/html; charset=utf-8'>
        <title>Diff Coverage</title>
        <style>
            .src-snippet { margin-top: 2em; }
            .src-name { font-weight: bold; }
            .snippets {
                border-top: 1px solid #bdbdbd;
                border-bottom: 1px solid #bdbdbd;
            }
            pre { line-height: 125%; }
td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.hll { background-color: #ffcccc }
.c { color: #3D7B7B; font-style: italic } /* Comment */
.err { border: 1px solid #FF0000 } /* Error */
.k { color: #008000; font-weight: bold } /* Keyword */
.o { color: #666666 } /* Operator */
.ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */
.cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */
.cp { color: #9C6500 } /* Comment.Preproc */
.cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */
.c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */
.cs { color: #3D7B7B; font-style: italic } /* Comment.Special */
.gd { color: #A00000 } /* Generic.Deleted */
.ge { font-style: italic } /* Generic.Emph */
.gr { color: #E40000 } /* Generic.Error */
.gh { color: #000080; font-weight: bold } /* Generic.Heading */
.gi { color: #008400 } /* Generic.Inserted */
.go { color: #717171 } /* Generic.Output */
.gp { color: #000080; font-weight: bold } /* Generic.Prompt */
.gs { font-weight: bold } /* Generic.Strong */
.gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.gt { color: #0044DD } /* Generic.Traceback */
.kc { color: #008000; font-weight: bold } /* Keyword.Constant */
.kd { color: #008000; font-weight: bold } /* Keyword.Declaration */
.kn { color: #008000; font-weight: bold } /* Keyword.Namespace */
.kp { color: #008000 } /* Keyword.Pseudo */
.kr { color: #008000; font-weight: bold } /* Keyword.Reserved */
.kt { color: #B00040 } /* Keyword.Type */
.m { color: #666666 } /* Literal.Number */
.s { color: #BA2121 } /* Literal.String */
.na { color: #687822 } /* Name.Attribute */
.nb { color: #008000 } /* Name.Builtin */
.nc { color: #0000FF; font-weight: bold } /* Name.Class */
.no { color: #880000 } /* Name.Constant */
.nd { color: #AA22FF } /* Name.Decorator */
.ni { color: #717171; font-weight: bold } /* Name.Entity */
.ne { color: #CB3F38; font-weight: bold } /* Name.Exception */
.nf { color: #0000FF } /* Name.Function */
.nl { color: #767600 } /* Name.Label */
.nn { color: #0000FF; font-weight: bold } /* Name.Namespace */
.nt { color: #008000; font-weight: bold } /* Name.Tag */
.nv { color: #19177C } /* Name.Variable */
.ow { color: #AA22FF; font-weight: bold } /* Operator.Word */
.w { color: #bbbbbb } /* Text.Whitespace */
.mb { color: #666666 } /* Literal.Number.Bin */
.mf { color: #666666 } /* Literal.Number.Float */
.mh { color: #666666 } /* Literal.Number.Hex */
.mi { color: #666666 } /* Literal.Number.Integer */
.mo { color: #666666 } /* Literal.Number.Oct */
.sa { color: #BA2121 } /* Literal.String.Affix */
.sb { color: #BA2121 } /* Literal.String.Backtick */
.sc { color: #BA2121 } /* Literal.String.Char */
.dl { color: #BA2121 } /* Literal.String.Delimiter */
.sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */
.s2 { color: #BA2121 } /* Literal.String.Double */
.se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */
.sh { color: #BA2121 } /* Literal.String.Heredoc */
.si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */
.sx { color: #008000 } /* Literal.String.Other */
.sr { color: #A45A77 } /* Literal.String.Regex */
.s1 { color: #BA2121 } /* Literal.String.Single */
.ss { color: #19177C } /* Literal.String.Symbol */
.bp { color: #008000 } /* Name.Builtin.Pseudo */
.fm { color: #0000FF } /* Name.Function.Magic */
.vc { color: #19177C } /* Name.Variable.Class */
.vg { color: #19177C } /* Name.Variable.Global */
.vi { color: #19177C } /* Name.Variable.Instance */
.vm { color: #19177C } /* Name.Variable.Magic */
.il { color: #666666 } /* Literal.Number.Integer.Long */
        </style>
    </head>
    <body>
        <h1>Diff Coverage</h1>
        <p>Diff: develop...HEAD, staged and unstaged changes</p>
        <ul>
            <li><b>Total</b>: 295 lines</li>
            <li><b>Missing</b>: 179 lines</li>
            <li><b>Coverage</b>: 39%</li>
        </ul>
        <table border="1">
            <tr>
                <th>Source File</th>
                <th>Diff Coverage (%)</th>
                <th>Missing Lines</th>
            </tr>
            <tr>
                <td>bkmonitor/alarm_backends/core/cache/strategy.py</td>
                <td>0.0%</td>
                <td>560-571</td>
            </tr>
            <tr>
                <td>bkmonitor/alarm_backends/core/storage/kafka.py</td>
                <td>19.6%</td>
                <td>39,94,105,154,167,169,173,176-178,180-183,189,193,203,210,236-239,252-255,257-259,265,267-268,274-275,277-279,281,295-296,303,313,317-319</td>
            </tr>
            <tr>
                <td>bkmonitor/alarm_backends/management/story/kernel_story.py</td>
                <td>0.0%</td>
                <td>80</td>
            </tr>
            <tr>
                <td>bkmonitor/alarm_backends/service/fta_action/tasks/action_tasks.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/api/bcs_cluster_manager/default.py</td>
                <td>75.0%</td>
                <td>60,62</td>
            </tr>
            <tr>
                <td>bkmonitor/api/bcs_helm_manager/default.py</td>
                <td>64.5%</td>
                <td>41,48-49,51,73-79</td>
            </tr>
            <tr>
                <td>bkmonitor/api/kubernetes/default.py</td>
                <td>10.5%</td>
                <td>701-704,706,708,719-726,739-740,742</td>
            </tr>
            <tr>
                <td>bkmonitor/bkmonitor/iam/action.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/bkmonitor/models/bcs_cluster.py</td>
                <td>27.9%</td>
                <td>40-41,43-45,244-245,344-347,349-352,357,368-371,407,410-411,415-416,421,425,436,439-440,447</td>
            </tr>
            <tr>
                <td>bkmonitor/bkmonitor/utils/cipher.py</td>
                <td>18.5%</td>
                <td>131,134-135,137,139,141-144,147-155,235-236,244-245</td>
            </tr>
            <tr>
                <td>bkmonitor/bkmonitor/utils/consul.py</td>
                <td>40.0%</td>
                <td>69-70,95</td>
            </tr>
            <tr>
                <td>bkmonitor/bkmonitor/utils/rsa/crypto.py</td>
                <td>47.5%</td>
                <td>15,20,22-23,25,27,30,33-34,37,40-41,43-44,54,59,61,64-66,69-70,73-74,77,79,81-83,94-96</td>
            </tr>
            <tr>
                <td>bkmonitor/config/__init__.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/config/default.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/config/role/worker.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/config/tools/consul.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/config/tools/elasticsearch.py</td>
                <td>66.7%</td>
                <td>30</td>
            </tr>
            <tr>
                <td>bkmonitor/config/tools/mysql.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/config/tools/rabbitmq.py</td>
                <td>66.7%</td>
                <td>41</td>
            </tr>
            <tr>
                <td>bkmonitor/config/tools/redis.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/manage.py</td>
                <td>100%</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>bkmonitor/packages/utils/redis_client.py</td>
                <td>66.7%</td>
                <td>68</td>
            </tr>
        </table>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/alarm_backends/core/cache/strategy.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">556</span>
<span class="normal">557</span>
<span class="normal">558</span>
<span class="normal">559</span>
<span class="normal">560</span>
<span class="normal">561</span>
<span class="normal">562</span>
<span class="normal">563</span>
<span class="normal">564</span>
<span class="normal">565</span>
<span class="normal">566</span>
<span class="normal">567</span>
<span class="normal">568</span>
<span class="normal">569</span>
<span class="normal">570</span>
<span class="normal">571</span>
<span class="normal">572</span>
<span class="normal">573</span>
<span class="normal">574</span>
<span class="normal">575</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-556" name="bkmonitor/alarm_backends/core/cache/strategy.py-556"></a>    <span class="k">def</span> <span class="nf">get_strategy_by_id</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">strategy_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-557" name="bkmonitor/alarm_backends/core/cache/strategy.py-557"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-558" name="bkmonitor/alarm_backends/core/cache/strategy.py-558"></a><span class="sd">        从缓存中获取策略详情</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-559" name="bkmonitor/alarm_backends/core/cache/strategy.py-559"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-560" name="bkmonitor/alarm_backends/core/cache/strategy.py-560"></a><span class="hll">        <span class="n">delay</span> <span class="o">=</span> <span class="mi">1</span>  <span class="c1"># 初始延迟1秒</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-561" name="bkmonitor/alarm_backends/core/cache/strategy.py-561"></a><span class="hll">        <span class="n">count</span> <span class="o">=</span> <span class="mi">1</span>  <span class="c1"># 最多循环3次</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-562" name="bkmonitor/alarm_backends/core/cache/strategy.py-562"></a><span class="hll">        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-563" name="bkmonitor/alarm_backends/core/cache/strategy.py-563"></a><span class="hll">            <span class="n">strategy</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="n">cache</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="n">CACHE_KEY_TEMPLATE</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">strategy_id</span><span class="o">=</span><span class="n">strategy_id</span><span class="p">))</span> <span class="ow">or</span> <span class="s2">&quot;null&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-564" name="bkmonitor/alarm_backends/core/cache/strategy.py-564"></a><span class="hll">            <span class="k">if</span> <span class="n">strategy</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-565" name="bkmonitor/alarm_backends/core/cache/strategy.py-565"></a><span class="hll">                <span class="k">break</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-566" name="bkmonitor/alarm_backends/core/cache/strategy.py-566"></a><span class="hll">            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;failed to get strategy from redis for </span><span class="si">{</span><span class="n">count</span><span class="si">}</span><span class="s2"> times. strategy(</span><span class="si">{</span><span class="n">strategy_id</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-567" name="bkmonitor/alarm_backends/core/cache/strategy.py-567"></a><span class="hll">            <span class="n">count</span> <span class="o">+=</span> <span class="mi">1</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-568" name="bkmonitor/alarm_backends/core/cache/strategy.py-568"></a><span class="hll">            <span class="k">if</span> <span class="n">count</span> <span class="o">&gt;</span> <span class="mi">3</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-569" name="bkmonitor/alarm_backends/core/cache/strategy.py-569"></a><span class="hll">                <span class="k">break</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-570" name="bkmonitor/alarm_backends/core/cache/strategy.py-570"></a><span class="hll">            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="n">delay</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-571" name="bkmonitor/alarm_backends/core/cache/strategy.py-571"></a><span class="hll">            <span class="n">delay</span> <span class="o">*=</span> <span class="mi">2</span>
</span><a id="bkmonitor/alarm_backends/core/cache/strategy.py-572" name="bkmonitor/alarm_backends/core/cache/strategy.py-572"></a>        <span class="c1"># strategy = json.loads(cls.cache.get(cls.CACHE_KEY_TEMPLATE.format(strategy_id=strategy_id)) or &quot;null&quot;)</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-573" name="bkmonitor/alarm_backends/core/cache/strategy.py-573"></a>        <span class="c1"># 兼容旧版策略</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-574" name="bkmonitor/alarm_backends/core/cache/strategy.py-574"></a>        <span class="n">strategy</span> <span class="o">=</span> <span class="n">Strategy</span><span class="o">.</span><span class="n">convert_v1_to_v2</span><span class="p">(</span><span class="n">strategy</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/cache/strategy.py-575" name="bkmonitor/alarm_backends/core/cache/strategy.py-575"></a>        <span class="k">return</span> <span class="n">strategy</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/alarm_backends/core/storage/kafka.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">35</span>
<span class="normal">36</span>
<span class="normal">37</span>
<span class="normal">38</span>
<span class="normal">39</span>
<span class="normal">40</span>
<span class="normal">41</span>
<span class="normal">42</span>
<span class="normal">43</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-35" name="bkmonitor/alarm_backends/core/storage/kafka.py-35"></a>        <span class="k">if</span> <span class="n">kfk_conf</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-36" name="bkmonitor/alarm_backends/core/storage/kafka.py-36"></a>            <span class="n">kafka_hosts</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">:</span><span class="si">{}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">kfk_conf</span><span class="p">[</span><span class="s2">&quot;domain&quot;</span><span class="p">],</span> <span class="n">kfk_conf</span><span class="p">[</span><span class="s2">&quot;port&quot;</span><span class="p">])</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-37" name="bkmonitor/alarm_backends/core/storage/kafka.py-37"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-38" name="bkmonitor/alarm_backends/core/storage/kafka.py-38"></a>            <span class="n">kafka_hosts</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">settings</span><span class="o">.</span><span class="n">KAFKA_HOST</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">settings</span><span class="o">.</span><span class="n">KAFKA_PORT</span><span class="si">}</span><span class="s2">&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-39" name="bkmonitor/alarm_backends/core/storage/kafka.py-39"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">kafka_hosts</span> <span class="o">=</span> <span class="n">kafka_hosts</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-40" name="bkmonitor/alarm_backends/core/storage/kafka.py-40"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">_client</span> <span class="o">=</span> <span class="n">kafka</span><span class="o">.</span><span class="n">SimpleClient</span><span class="p">(</span><span class="n">hosts</span><span class="o">=</span><span class="n">kafka_hosts</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="n">timeout</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-41" name="bkmonitor/alarm_backends/core/storage/kafka.py-41"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">_client_init_time</span> <span class="o">=</span> <span class="n">arrow</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-42" name="bkmonitor/alarm_backends/core/storage/kafka.py-42"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">producer</span> <span class="o">=</span> <span class="n">kafka</span><span class="o">.</span><span class="n">producer</span><span class="o">.</span><span class="n">SimpleProducer</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">client</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-43" name="bkmonitor/alarm_backends/core/storage/kafka.py-43"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">consumer_pool</span> <span class="o">=</span> <span class="p">{}</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">90</span>
<span class="normal">91</span>
<span class="normal">92</span>
<span class="normal">93</span>
<span class="normal">94</span>
<span class="normal">95</span>
<span class="normal">96</span>
<span class="normal">97</span>
<span class="normal">98</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-90" name="bkmonitor/alarm_backends/core/storage/kafka.py-90"></a>        <span class="c1"># 增加初始化判断机制 关键值未赋值 则初始化不成功</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-91" name="bkmonitor/alarm_backends/core/storage/kafka.py-91"></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">offset_manager_pool</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">offset_manager_pool_key</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-92" name="bkmonitor/alarm_backends/core/storage/kafka.py-92"></a>            <span class="n">offset_manager</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">offset_manager_pool</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">offset_manager_pool_key</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-93" name="bkmonitor/alarm_backends/core/storage/kafka.py-93"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-94" name="bkmonitor/alarm_backends/core/storage/kafka.py-94"></a><span class="hll">            <span class="n">offset_manager</span> <span class="o">=</span> <span class="n">KafkaOffsetManager</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">get_consumer</span><span class="p">(),</span> <span class="bp">self</span><span class="o">.</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-95" name="bkmonitor/alarm_backends/core/storage/kafka.py-95"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">offset_manager_pool</span><span class="p">[</span><span class="n">offset_manager_pool_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">offset_manager</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-96" name="bkmonitor/alarm_backends/core/storage/kafka.py-96"></a>        <span class="k">return</span> <span class="n">offset_manager</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-97" name="bkmonitor/alarm_backends/core/storage/kafka.py-97"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-98" name="bkmonitor/alarm_backends/core/storage/kafka.py-98"></a>    <span class="c1"># 重试3次 确认offsets赋值成功</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">101</span>
<span class="normal">102</span>
<span class="normal">103</span>
<span class="normal">104</span>
<span class="normal">105</span>
<span class="normal">106</span>
<span class="normal">107</span>
<span class="normal">108</span>
<span class="normal">109</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-101" name="bkmonitor/alarm_backends/core/storage/kafka.py-101"></a>            <span class="k">try</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-102" name="bkmonitor/alarm_backends/core/storage/kafka.py-102"></a>                <span class="c1"># consumer = kafka.consumer.SimpleConsumer(</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-103" name="bkmonitor/alarm_backends/core/storage/kafka.py-103"></a>                <span class="c1">#     self.client, group_name, topic, auto_commit=settings.KAFKA_AUTO_COMMIT, max_buffer_size=None</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-104" name="bkmonitor/alarm_backends/core/storage/kafka.py-104"></a>                <span class="c1"># )</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-105" name="bkmonitor/alarm_backends/core/storage/kafka.py-105"></a><span class="hll">                <span class="n">consumer</span> <span class="o">=</span> <span class="n">KafkaConsumer</span><span class="p">(</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-106" name="bkmonitor/alarm_backends/core/storage/kafka.py-106"></a>                    <span class="n">bootstrap_servers</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">kafka_hosts</span><span class="p">,</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-107" name="bkmonitor/alarm_backends/core/storage/kafka.py-107"></a>                    <span class="n">group_id</span><span class="o">=</span><span class="n">group_name</span><span class="p">,</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-108" name="bkmonitor/alarm_backends/core/storage/kafka.py-108"></a>                    <span class="n">enable_auto_commit</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>  <span class="c1"># 禁用自动提交偏移量</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-109" name="bkmonitor/alarm_backends/core/storage/kafka.py-109"></a>                    <span class="c1"># fetch_min_bytes=1,  # 最小拉取消息字节数</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">150</span>
<span class="normal">151</span>
<span class="normal">152</span>
<span class="normal">153</span>
<span class="normal">154</span>
<span class="normal">155</span>
<span class="normal">156</span>
<span class="normal">157</span>
<span class="normal">158</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-150" name="bkmonitor/alarm_backends/core/storage/kafka.py-150"></a>        <span class="k">if</span> <span class="n">force_offset</span> <span class="o">&gt;=</span> <span class="mi">0</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-151" name="bkmonitor/alarm_backends/core/storage/kafka.py-151"></a>            <span class="n">new_offset</span> <span class="o">=</span> <span class="n">force_offset</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-152" name="bkmonitor/alarm_backends/core/storage/kafka.py-152"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-153" name="bkmonitor/alarm_backends/core/storage/kafka.py-153"></a>            <span class="n">offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset_manager</span><span class="p">()</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-154" name="bkmonitor/alarm_backends/core/storage/kafka.py-154"></a><span class="hll">            <span class="n">tail</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset_manager</span><span class="p">()</span><span class="o">.</span><span class="n">get_tail</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">topic</span><span class="p">)</span> <span class="o">-</span> <span class="mi">5</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-155" name="bkmonitor/alarm_backends/core/storage/kafka.py-155"></a>            <span class="n">new_offset</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">offset</span><span class="p">,</span> <span class="n">tail</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-156" name="bkmonitor/alarm_backends/core/storage/kafka.py-156"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">get_offset_manager</span><span class="p">()</span><span class="o">.</span><span class="n">set_offset</span><span class="p">(</span><span class="n">new_offset</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-157" name="bkmonitor/alarm_backends/core/storage/kafka.py-157"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-158" name="bkmonitor/alarm_backends/core/storage/kafka.py-158"></a>    <span class="k">def</span> <span class="nf">take_raw</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mi">5</span><span class="p">):</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">163</span>
<span class="normal">164</span>
<span class="normal">165</span>
<span class="normal">166</span>
<span class="normal">167</span>
<span class="normal">168</span>
<span class="normal">169</span>
<span class="normal">170</span>
<span class="normal">171</span>
<span class="normal">172</span>
<span class="normal">173</span>
<span class="normal">174</span>
<span class="normal">175</span>
<span class="normal">176</span>
<span class="normal">177</span>
<span class="normal">178</span>
<span class="normal">179</span>
<span class="normal">180</span>
<span class="normal">181</span>
<span class="normal">182</span>
<span class="normal">183</span>
<span class="normal">184</span>
<span class="normal">185</span>
<span class="normal">186</span>
<span class="normal">187</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-163" name="bkmonitor/alarm_backends/core/storage/kafka.py-163"></a><span class="sd">            timeout: 超时时间(秒)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-164" name="bkmonitor/alarm_backends/core/storage/kafka.py-164"></a><span class="sd">        Returns:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-165" name="bkmonitor/alarm_backends/core/storage/kafka.py-165"></a><span class="sd">            messages: 消息列表</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-166" name="bkmonitor/alarm_backends/core/storage/kafka.py-166"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-167" name="bkmonitor/alarm_backends/core/storage/kafka.py-167"></a><span class="hll">        <span class="n">messages</span> <span class="o">=</span> <span class="p">[]</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-168" name="bkmonitor/alarm_backends/core/storage/kafka.py-168"></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">redis_offset</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-169" name="bkmonitor/alarm_backends/core/storage/kafka.py-169"></a><span class="hll">            <span class="bp">self</span><span class="o">.</span><span class="n">get_offset_manager</span><span class="p">()</span><span class="o">.</span><span class="n">reset_consumer_offset</span><span class="p">(</span><span class="n">count</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-170" name="bkmonitor/alarm_backends/core/storage/kafka.py-170"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-171" name="bkmonitor/alarm_backends/core/storage/kafka.py-171"></a>        <span class="k">try</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-172" name="bkmonitor/alarm_backends/core/storage/kafka.py-172"></a>            <span class="c1"># poll返回一个字典 {TopicPartition: [messages]}</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-173" name="bkmonitor/alarm_backends/core/storage/kafka.py-173"></a><span class="hll">            <span class="n">records</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_consumer</span><span class="p">()</span><span class="o">.</span><span class="n">poll</span><span class="p">(</span><span class="n">timeout_ms</span><span class="o">=</span><span class="n">timeout</span><span class="o">*</span><span class="mi">1000</span><span class="p">,</span> <span class="n">max_records</span><span class="o">=</span><span class="n">count</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-174" name="bkmonitor/alarm_backends/core/storage/kafka.py-174"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-175" name="bkmonitor/alarm_backends/core/storage/kafka.py-175"></a>            <span class="c1"># 处理获取到的消息</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-176" name="bkmonitor/alarm_backends/core/storage/kafka.py-176"></a><span class="hll">            <span class="k">for</span> <span class="n">tp</span><span class="p">,</span> <span class="n">msgs</span> <span class="ow">in</span> <span class="n">records</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-177" name="bkmonitor/alarm_backends/core/storage/kafka.py-177"></a><span class="hll">                <span class="k">for</span> <span class="n">msg</span> <span class="ow">in</span> <span class="n">msgs</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-178" name="bkmonitor/alarm_backends/core/storage/kafka.py-178"></a><span class="hll">                    <span class="n">messages</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">msg</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-179" name="bkmonitor/alarm_backends/core/storage/kafka.py-179"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-180" name="bkmonitor/alarm_backends/core/storage/kafka.py-180"></a><span class="hll">        <span class="k">except</span> <span class="n">kafka</span><span class="o">.</span><span class="n">errors</span><span class="o">.</span><span class="n">KafkaError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-181" name="bkmonitor/alarm_backends/core/storage/kafka.py-181"></a><span class="hll">            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;Kafka error while fetching messages: </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">e</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-182" name="bkmonitor/alarm_backends/core/storage/kafka.py-182"></a><span class="hll">        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-183" name="bkmonitor/alarm_backends/core/storage/kafka.py-183"></a><span class="hll">            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;get kafka info error&quot;</span><span class="p">,</span> <span class="n">e</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-184" name="bkmonitor/alarm_backends/core/storage/kafka.py-184"></a>        <span class="k">finally</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-185" name="bkmonitor/alarm_backends/core/storage/kafka.py-185"></a>            <span class="k">if</span> <span class="n">settings</span><span class="o">.</span><span class="n">KAFKA_AUTO_COMMIT</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-186" name="bkmonitor/alarm_backends/core/storage/kafka.py-186"></a>                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_consumer</span><span class="p">()</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-187" name="bkmonitor/alarm_backends/core/storage/kafka.py-187"></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;Kafka commit failure&quot;</span><span class="p">)</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">185</span>
<span class="normal">186</span>
<span class="normal">187</span>
<span class="normal">188</span>
<span class="normal">189</span>
<span class="normal">190</span>
<span class="normal">191</span>
<span class="normal">192</span>
<span class="normal">193</span>
<span class="normal">194</span>
<span class="normal">195</span>
<span class="normal">196</span>
<span class="normal">197</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-185" name="bkmonitor/alarm_backends/core/storage/kafka.py-185"></a>            <span class="k">if</span> <span class="n">settings</span><span class="o">.</span><span class="n">KAFKA_AUTO_COMMIT</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-186" name="bkmonitor/alarm_backends/core/storage/kafka.py-186"></a>                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_consumer</span><span class="p">()</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-187" name="bkmonitor/alarm_backends/core/storage/kafka.py-187"></a>                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;Kafka commit failure&quot;</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-188" name="bkmonitor/alarm_backends/core/storage/kafka.py-188"></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">redis_offset</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-189" name="bkmonitor/alarm_backends/core/storage/kafka.py-189"></a><span class="hll">            <span class="bp">self</span><span class="o">.</span><span class="n">get_offset_manager</span><span class="p">()</span><span class="o">.</span><span class="n">update_consumer_offset</span><span class="p">(</span><span class="n">count</span><span class="p">,</span> <span class="n">messages</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-190" name="bkmonitor/alarm_backends/core/storage/kafka.py-190"></a>        <span class="k">return</span> <span class="n">messages</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-191" name="bkmonitor/alarm_backends/core/storage/kafka.py-191"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-192" name="bkmonitor/alarm_backends/core/storage/kafka.py-192"></a>    <span class="k">def</span> <span class="nf">take</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">timeout</span><span class="o">=</span><span class="mf">0.1</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-193" name="bkmonitor/alarm_backends/core/storage/kafka.py-193"></a><span class="hll">        <span class="k">return</span> <span class="p">[</span><span class="n">m</span><span class="o">.</span><span class="n">value</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">take_raw</span><span class="p">(</span><span class="n">count</span><span class="p">,</span> <span class="n">timeout</span><span class="p">)]</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-194" name="bkmonitor/alarm_backends/core/storage/kafka.py-194"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-195" name="bkmonitor/alarm_backends/core/storage/kafka.py-195"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-196" name="bkmonitor/alarm_backends/core/storage/kafka.py-196"></a><span class="k">class</span> <span class="nc">KafkaOffsetManager</span><span class="p">(</span><span class="nb">object</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-197" name="bkmonitor/alarm_backends/core/storage/kafka.py-197"></a>    <span class="n">TIMEOUT</span> <span class="o">=</span> <span class="n">CONST_ONE_DAY</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">199</span>
<span class="normal">200</span>
<span class="normal">201</span>
<span class="normal">202</span>
<span class="normal">203</span>
<span class="normal">204</span>
<span class="normal">205</span>
<span class="normal">206</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-199" name="bkmonitor/alarm_backends/core/storage/kafka.py-199"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-200" name="bkmonitor/alarm_backends/core/storage/kafka.py-200"></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">consumer</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-201" name="bkmonitor/alarm_backends/core/storage/kafka.py-201"></a>        <span class="c1"># consumer和topic需要在self.get_offset()方法使用，因此需要放到前面</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-202" name="bkmonitor/alarm_backends/core/storage/kafka.py-202"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span> <span class="o">=</span> <span class="n">consumer</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-203" name="bkmonitor/alarm_backends/core/storage/kafka.py-203"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">topic</span> <span class="o">=</span> <span class="n">topic</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-204" name="bkmonitor/alarm_backends/core/storage/kafka.py-204"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">cache</span> <span class="o">=</span> <span class="n">Cache</span><span class="p">(</span><span class="s2">&quot;service&quot;</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-205" name="bkmonitor/alarm_backends/core/storage/kafka.py-205"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-206" name="bkmonitor/alarm_backends/core/storage/kafka.py-206"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">reset_offset</span> <span class="o">=</span> <span class="mi">0</span>  <span class="c1"># 当前的重置点</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">206</span>
<span class="normal">207</span>
<span class="normal">208</span>
<span class="normal">209</span>
<span class="normal">210</span>
<span class="normal">211</span>
<span class="normal">212</span>
<span class="normal">213</span>
<span class="normal">214</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-206" name="bkmonitor/alarm_backends/core/storage/kafka.py-206"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">reset_offset</span> <span class="o">=</span> <span class="mi">0</span>  <span class="c1"># 当前的重置点</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-207" name="bkmonitor/alarm_backends/core/storage/kafka.py-207"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-208" name="bkmonitor/alarm_backends/core/storage/kafka.py-208"></a>    <span class="nd">@property</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-209" name="bkmonitor/alarm_backends/core/storage/kafka.py-209"></a>    <span class="k">def</span> <span class="nf">key</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-210" name="bkmonitor/alarm_backends/core/storage/kafka.py-210"></a><span class="hll">        <span class="k">return</span> <span class="s2">&quot;_&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="nb">str</span><span class="p">,</span> <span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">KEY_PREFIX</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">config</span><span class="p">[</span><span class="s2">&quot;group_id&quot;</span><span class="p">],</span> <span class="bp">self</span><span class="o">.</span><span class="n">topic</span><span class="p">]))</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-211" name="bkmonitor/alarm_backends/core/storage/kafka.py-211"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-212" name="bkmonitor/alarm_backends/core/storage/kafka.py-212"></a>    <span class="nd">@property</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-213" name="bkmonitor/alarm_backends/core/storage/kafka.py-213"></a>    <span class="k">def</span> <span class="nf">reset_key</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-214" name="bkmonitor/alarm_backends/core/storage/kafka.py-214"></a>        <span class="k">return</span> <span class="s2">&quot;RESET_</span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">232</span>
<span class="normal">233</span>
<span class="normal">234</span>
<span class="normal">235</span>
<span class="normal">236</span>
<span class="normal">237</span>
<span class="normal">238</span>
<span class="normal">239</span>
<span class="normal">240</span>
<span class="normal">241</span>
<span class="normal">242</span>
<span class="normal">243</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-232" name="bkmonitor/alarm_backends/core/storage/kafka.py-232"></a>    <span class="k">def</span> <span class="nf">set_seek</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-233" name="bkmonitor/alarm_backends/core/storage/kafka.py-233"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-234" name="bkmonitor/alarm_backends/core/storage/kafka.py-234"></a><span class="sd">        修改：使用kafkaconsumer设置seek</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-235" name="bkmonitor/alarm_backends/core/storage/kafka.py-235"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-236" name="bkmonitor/alarm_backends/core/storage/kafka.py-236"></a><span class="hll">        <span class="n">partitions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_partitions</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-237" name="bkmonitor/alarm_backends/core/storage/kafka.py-237"></a><span class="hll">        <span class="n">remote_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_end_offset</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-238" name="bkmonitor/alarm_backends/core/storage/kafka.py-238"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">assign</span><span class="p">(</span><span class="n">partitions</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-239" name="bkmonitor/alarm_backends/core/storage/kafka.py-239"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">seek</span><span class="p">(</span><span class="n">partitions</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">remote_offset</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-240" name="bkmonitor/alarm_backends/core/storage/kafka.py-240"></a>        <span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">key</span><span class="p">,</span> <span class="n">remote_offset</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">TIMEOUT</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-241" name="bkmonitor/alarm_backends/core/storage/kafka.py-241"></a>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-242" name="bkmonitor/alarm_backends/core/storage/kafka.py-242"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-243" name="bkmonitor/alarm_backends/core/storage/kafka.py-243"></a>    <span class="k">def</span> <span class="nf">set_reset_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">offset</span><span class="p">):</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">248</span>
<span class="normal">249</span>
<span class="normal">250</span>
<span class="normal">251</span>
<span class="normal">252</span>
<span class="normal">253</span>
<span class="normal">254</span>
<span class="normal">255</span>
<span class="normal">256</span>
<span class="normal">257</span>
<span class="normal">258</span>
<span class="normal">259</span>
<span class="normal">260</span>
<span class="normal">261</span>
<span class="normal">262</span>
<span class="normal">263</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-248" name="bkmonitor/alarm_backends/core/storage/kafka.py-248"></a>    <span class="k">def</span> <span class="nf">get_partitions</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-249" name="bkmonitor/alarm_backends/core/storage/kafka.py-249"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-250" name="bkmonitor/alarm_backends/core/storage/kafka.py-250"></a><span class="sd">        新增：获取TopicPartition类型的partitions</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-251" name="bkmonitor/alarm_backends/core/storage/kafka.py-251"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-252" name="bkmonitor/alarm_backends/core/storage/kafka.py-252"></a><span class="hll">        <span class="n">partitions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">partitions_for_topic</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-253" name="bkmonitor/alarm_backends/core/storage/kafka.py-253"></a><span class="hll">        <span class="k">if</span> <span class="n">partitions</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-254" name="bkmonitor/alarm_backends/core/storage/kafka.py-254"></a><span class="hll">            <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">topics</span><span class="p">()</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-255" name="bkmonitor/alarm_backends/core/storage/kafka.py-255"></a><span class="hll">            <span class="n">partitions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">partitions_for_topic</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-256" name="bkmonitor/alarm_backends/core/storage/kafka.py-256"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-257" name="bkmonitor/alarm_backends/core/storage/kafka.py-257"></a><span class="hll">        <span class="n">partitions</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">}</span> <span class="k">if</span> <span class="n">partitions</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">partitions</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-258" name="bkmonitor/alarm_backends/core/storage/kafka.py-258"></a><span class="hll">        <span class="n">final_partitions</span> <span class="o">=</span> <span class="p">[</span><span class="n">TopicPartition</span><span class="p">(</span><span class="n">topic</span><span class="o">=</span><span class="n">topic</span><span class="p">,</span> <span class="n">partition</span><span class="o">=</span><span class="n">tp</span><span class="p">)</span> <span class="k">for</span> <span class="n">tp</span> <span class="ow">in</span> <span class="n">partitions</span><span class="p">]</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-259" name="bkmonitor/alarm_backends/core/storage/kafka.py-259"></a><span class="hll">        <span class="k">return</span> <span class="n">final_partitions</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-260" name="bkmonitor/alarm_backends/core/storage/kafka.py-260"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-261" name="bkmonitor/alarm_backends/core/storage/kafka.py-261"></a>    <span class="k">def</span> <span class="nf">_set_consumer_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">new_remote_offset</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-262" name="bkmonitor/alarm_backends/core/storage/kafka.py-262"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-263" name="bkmonitor/alarm_backends/core/storage/kafka.py-263"></a><span class="sd">        修改：修改设置consumer_offset方式使用assign和seek</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">261</span>
<span class="normal">262</span>
<span class="normal">263</span>
<span class="normal">264</span>
<span class="normal">265</span>
<span class="normal">266</span>
<span class="normal">267</span>
<span class="normal">268</span>
<span class="normal">269</span>
<span class="normal">270</span>
<span class="normal">271</span>
<span class="normal">272</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-261" name="bkmonitor/alarm_backends/core/storage/kafka.py-261"></a>    <span class="k">def</span> <span class="nf">_set_consumer_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">new_remote_offset</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-262" name="bkmonitor/alarm_backends/core/storage/kafka.py-262"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-263" name="bkmonitor/alarm_backends/core/storage/kafka.py-263"></a><span class="sd">        修改：修改设置consumer_offset方式使用assign和seek</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-264" name="bkmonitor/alarm_backends/core/storage/kafka.py-264"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-265" name="bkmonitor/alarm_backends/core/storage/kafka.py-265"></a><span class="hll">        <span class="n">partitions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_partitions</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-266" name="bkmonitor/alarm_backends/core/storage/kafka.py-266"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-267" name="bkmonitor/alarm_backends/core/storage/kafka.py-267"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">assign</span><span class="p">(</span><span class="n">partitions</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-268" name="bkmonitor/alarm_backends/core/storage/kafka.py-268"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">seek</span><span class="p">(</span><span class="n">partitions</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">new_remote_offset</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-269" name="bkmonitor/alarm_backends/core/storage/kafka.py-269"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-270" name="bkmonitor/alarm_backends/core/storage/kafka.py-270"></a>    <span class="k">def</span> <span class="nf">get_end_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-271" name="bkmonitor/alarm_backends/core/storage/kafka.py-271"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-272" name="bkmonitor/alarm_backends/core/storage/kafka.py-272"></a><span class="sd">        新增：获取最新offset</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">270</span>
<span class="normal">271</span>
<span class="normal">272</span>
<span class="normal">273</span>
<span class="normal">274</span>
<span class="normal">275</span>
<span class="normal">276</span>
<span class="normal">277</span>
<span class="normal">278</span>
<span class="normal">279</span>
<span class="normal">280</span>
<span class="normal">281</span>
<span class="normal">282</span>
<span class="normal">283</span>
<span class="normal">284</span>
<span class="normal">285</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-270" name="bkmonitor/alarm_backends/core/storage/kafka.py-270"></a>    <span class="k">def</span> <span class="nf">get_end_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-271" name="bkmonitor/alarm_backends/core/storage/kafka.py-271"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-272" name="bkmonitor/alarm_backends/core/storage/kafka.py-272"></a><span class="sd">        新增：获取最新offset</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-273" name="bkmonitor/alarm_backends/core/storage/kafka.py-273"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-274" name="bkmonitor/alarm_backends/core/storage/kafka.py-274"></a><span class="hll">        <span class="n">partitions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_partitions</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-275" name="bkmonitor/alarm_backends/core/storage/kafka.py-275"></a><span class="hll">        <span class="n">end_offsets</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">end_offsets</span><span class="p">(</span><span class="n">partitions</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-276" name="bkmonitor/alarm_backends/core/storage/kafka.py-276"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-277" name="bkmonitor/alarm_backends/core/storage/kafka.py-277"></a><span class="hll">        <span class="n">offset</span> <span class="o">=</span> <span class="p">[]</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-278" name="bkmonitor/alarm_backends/core/storage/kafka.py-278"></a><span class="hll">        <span class="k">for</span> <span class="n">partition</span> <span class="ow">in</span> <span class="n">partitions</span><span class="p">:</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-279" name="bkmonitor/alarm_backends/core/storage/kafka.py-279"></a><span class="hll">            <span class="n">offset</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">end_offsets</span><span class="p">[</span><span class="n">partition</span><span class="p">])</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-280" name="bkmonitor/alarm_backends/core/storage/kafka.py-280"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-281" name="bkmonitor/alarm_backends/core/storage/kafka.py-281"></a><span class="hll">        <span class="k">return</span> <span class="nb">max</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="mi">0</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-282" name="bkmonitor/alarm_backends/core/storage/kafka.py-282"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-283" name="bkmonitor/alarm_backends/core/storage/kafka.py-283"></a>    <span class="k">def</span> <span class="nf">reset_consumer_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">count</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-284" name="bkmonitor/alarm_backends/core/storage/kafka.py-284"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-285" name="bkmonitor/alarm_backends/core/storage/kafka.py-285"></a><span class="sd">        修改：当_get_offset()为空时，从end_offset设置</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">291</span>
<span class="normal">292</span>
<span class="normal">293</span>
<span class="normal">294</span>
<span class="normal">295</span>
<span class="normal">296</span>
<span class="normal">297</span>
<span class="normal">298</span>
<span class="normal">299</span>
<span class="normal">300</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-291" name="bkmonitor/alarm_backends/core/storage/kafka.py-291"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reset_offset</span> <span class="o">=</span> <span class="n">reset_offset</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-292" name="bkmonitor/alarm_backends/core/storage/kafka.py-292"></a>        <span class="c1"># 如果第一次读这个 topic，那么当前游标设置为最新前 3 条</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-293" name="bkmonitor/alarm_backends/core/storage/kafka.py-293"></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_offset</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-294" name="bkmonitor/alarm_backends/core/storage/kafka.py-294"></a>            <span class="c1"># 修改：此处拿end_offset</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-295" name="bkmonitor/alarm_backends/core/storage/kafka.py-295"></a><span class="hll">            <span class="n">max_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_end_offset</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-296" name="bkmonitor/alarm_backends/core/storage/kafka.py-296"></a><span class="hll">            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="n">max_offset</span> <span class="o">-</span> <span class="mi">3</span> <span class="k">if</span> <span class="n">max_offset</span> <span class="o">&gt;=</span> <span class="mi">3</span> <span class="k">else</span> <span class="mi">0</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-297" name="bkmonitor/alarm_backends/core/storage/kafka.py-297"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-298" name="bkmonitor/alarm_backends/core/storage/kafka.py-298"></a>        <span class="c1"># 否则从 redis 读取游标</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-299" name="bkmonitor/alarm_backends/core/storage/kafka.py-299"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-300" name="bkmonitor/alarm_backends/core/storage/kafka.py-300"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">299</span>
<span class="normal">300</span>
<span class="normal">301</span>
<span class="normal">302</span>
<span class="normal">303</span>
<span class="normal">304</span>
<span class="normal">305</span>
<span class="normal">306</span>
<span class="normal">307</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-299" name="bkmonitor/alarm_backends/core/storage/kafka.py-299"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-300" name="bkmonitor/alarm_backends/core/storage/kafka.py-300"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-301" name="bkmonitor/alarm_backends/core/storage/kafka.py-301"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-302" name="bkmonitor/alarm_backends/core/storage/kafka.py-302"></a>        <span class="c1"># 更新 client 的游标</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-303" name="bkmonitor/alarm_backends/core/storage/kafka.py-303"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">_set_consumer_offset</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span><span class="p">,</span> <span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-304" name="bkmonitor/alarm_backends/core/storage/kafka.py-304"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-305" name="bkmonitor/alarm_backends/core/storage/kafka.py-305"></a>        <span class="n">new_local_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">+</span> <span class="n">count</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-306" name="bkmonitor/alarm_backends/core/storage/kafka.py-306"></a>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span> <span class="o">&lt;</span> <span class="n">new_local_offset</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-307" name="bkmonitor/alarm_backends/core/storage/kafka.py-307"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_offset</span><span class="p">(</span><span class="n">new_local_offset</span><span class="p">)</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">309</span>
<span class="normal">310</span>
<span class="normal">311</span>
<span class="normal">312</span>
<span class="normal">313</span>
<span class="normal">314</span>
<span class="normal">315</span>
<span class="normal">316</span>
<span class="normal">317</span>
<span class="normal">318</span>
<span class="normal">319</span>
<span class="normal">320</span>
<span class="normal">321</span>
<span class="normal">322</span>
<span class="normal">323</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-309" name="bkmonitor/alarm_backends/core/storage/kafka.py-309"></a>    <span class="k">def</span> <span class="nf">get_tail</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-310" name="bkmonitor/alarm_backends/core/storage/kafka.py-310"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-311" name="bkmonitor/alarm_backends/core/storage/kafka.py-311"></a><span class="sd">        修改：获取最大offset，可以拿end_offset</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-312" name="bkmonitor/alarm_backends/core/storage/kafka.py-312"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-313" name="bkmonitor/alarm_backends/core/storage/kafka.py-313"></a><span class="hll">        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_end_offset</span><span class="p">(</span><span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-314" name="bkmonitor/alarm_backends/core/storage/kafka.py-314"></a>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-315" name="bkmonitor/alarm_backends/core/storage/kafka.py-315"></a>    <span class="k">def</span> <span class="nf">update_consumer_offset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">count</span><span class="p">,</span> <span class="n">messages</span><span class="p">,</span> <span class="n">topic</span><span class="p">):</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-316" name="bkmonitor/alarm_backends/core/storage/kafka.py-316"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">messages</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-317" name="bkmonitor/alarm_backends/core/storage/kafka.py-317"></a><span class="hll">            <span class="n">partition</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_partitions</span><span class="p">(</span><span class="n">topic</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-318" name="bkmonitor/alarm_backends/core/storage/kafka.py-318"></a><span class="hll">            <span class="n">new_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">committed</span><span class="p">(</span><span class="n">partition</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-319" name="bkmonitor/alarm_backends/core/storage/kafka.py-319"></a><span class="hll">            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_offset</span><span class="p">(</span><span class="n">new_offset</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/core/storage/kafka.py-320" name="bkmonitor/alarm_backends/core/storage/kafka.py-320"></a>        <span class="k">elif</span> <span class="nb">len</span><span class="p">(</span><span class="n">messages</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">count</span><span class="p">:</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-321" name="bkmonitor/alarm_backends/core/storage/kafka.py-321"></a>            <span class="n">offset</span> <span class="o">=</span> <span class="n">messages</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">offset</span> <span class="o">+</span> <span class="mi">1</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-322" name="bkmonitor/alarm_backends/core/storage/kafka.py-322"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Kafka_offset local_desc </span><span class="si">%s</span><span class="s2">: </span><span class="si">%s</span><span class="s2"> to </span><span class="si">%s</span><span class="s2">&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">key</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span><span class="p">,</span> <span class="n">offset</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/core/storage/kafka.py-323" name="bkmonitor/alarm_backends/core/storage/kafka.py-323"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">instance_offset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">set_offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/alarm_backends/management/story/kernel_story.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">76</span>
<span class="normal">77</span>
<span class="normal">78</span>
<span class="normal">79</span>
<span class="normal">80</span>
<span class="normal">81</span>
<span class="normal">82</span>
<span class="normal">83</span>
<span class="normal">84</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/alarm_backends/management/story/kernel_story.py-76" name="bkmonitor/alarm_backends/management/story/kernel_story.py-76"></a>        <span class="n">kafka_queue</span> <span class="o">=</span> <span class="n">KafkaQueue</span><span class="o">.</span><span class="n">get_common_kafka_queue</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-77" name="bkmonitor/alarm_backends/management/story/kernel_story.py-77"></a>        <span class="n">data_id</span> <span class="o">=</span> <span class="n">topic_info</span><span class="p">[</span><span class="s2">&quot;data_id&quot;</span><span class="p">]</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-78" name="bkmonitor/alarm_backends/management/story/kernel_story.py-78"></a>        <span class="c1"># 获取本机GSE EVENT 对应topic消费组对应的最新的offset， 再和本地offset对比。</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-79" name="bkmonitor/alarm_backends/management/story/kernel_story.py-79"></a>        <span class="n">kafka_queue</span><span class="o">.</span><span class="n">set_topic</span><span class="p">(</span><span class="n">topic</span><span class="p">,</span> <span class="n">group_prefix</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;access.event.</span><span class="si">{</span><span class="n">data_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-80" name="bkmonitor/alarm_backends/management/story/kernel_story.py-80"></a><span class="hll">        <span class="n">offset_manager</span> <span class="o">=</span> <span class="n">KafkaOffsetManager</span><span class="p">(</span><span class="n">kafka_queue</span><span class="o">.</span><span class="n">get_consumer</span><span class="p">(),</span> <span class="n">topic</span><span class="p">)</span>
</span><a id="bkmonitor/alarm_backends/management/story/kernel_story.py-81" name="bkmonitor/alarm_backends/management/story/kernel_story.py-81"></a>        <span class="n">last_remote_offset</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">offset_manager</span><span class="o">.</span><span class="n">consumer</span><span class="o">.</span><span class="n">offsets</span><span class="o">.</span><span class="n">values</span><span class="p">())</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-82" name="bkmonitor/alarm_backends/management/story/kernel_story.py-82"></a>        <span class="n">local_offset</span> <span class="o">=</span> <span class="n">offset_manager</span><span class="o">.</span><span class="n">get_offset</span><span class="p">()</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-83" name="bkmonitor/alarm_backends/management/story/kernel_story.py-83"></a>        <span class="n">problem</span> <span class="o">=</span> <span class="kc">None</span>
<a id="bkmonitor/alarm_backends/management/story/kernel_story.py-84" name="bkmonitor/alarm_backends/management/story/kernel_story.py-84"></a>        <span class="k">if</span> <span class="n">local_offset</span> <span class="o">+</span> <span class="mi">10000</span> <span class="o">&lt;=</span> <span class="n">last_remote_offset</span><span class="p">:</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/api/bcs_cluster_manager/default.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">56</span>
<span class="normal">57</span>
<span class="normal">58</span>
<span class="normal">59</span>
<span class="normal">60</span>
<span class="normal">61</span>
<span class="normal">62</span>
<span class="normal">63</span>
<span class="normal">64</span>
<span class="normal">65</span>
<span class="normal">66</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/bcs_cluster_manager/default.py-56" name="bkmonitor/api/bcs_cluster_manager/default.py-56"></a>    <span class="k">class</span> <span class="nc">RequestSerializer</span><span class="p">(</span><span class="n">serializers</span><span class="o">.</span><span class="n">Serializer</span><span class="p">):</span>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-57" name="bkmonitor/api/bcs_cluster_manager/default.py-57"></a>        <span class="n">cluster_id</span> <span class="o">=</span> <span class="n">serializers</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">required</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">label</span><span class="o">=</span><span class="s2">&quot;集群ID&quot;</span><span class="p">)</span>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-58" name="bkmonitor/api/bcs_cluster_manager/default.py-58"></a>    
<a id="bkmonitor/api/bcs_cluster_manager/default.py-59" name="bkmonitor/api/bcs_cluster_manager/default.py-59"></a>    <span class="k">def</span> <span class="nf">perform_request</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">params</span><span class="p">):</span>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-60" name="bkmonitor/api/bcs_cluster_manager/default.py-60"></a><span class="hll">        <span class="n">masters</span> <span class="o">=</span> <span class="nb">super</span><span class="p">(</span><span class="n">FetchMastersResource</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="n">perform_request</span><span class="p">(</span><span class="n">params</span><span class="p">)</span>
</span><a id="bkmonitor/api/bcs_cluster_manager/default.py-61" name="bkmonitor/api/bcs_cluster_manager/default.py-61"></a>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-62" name="bkmonitor/api/bcs_cluster_manager/default.py-62"></a><span class="hll">        <span class="k">return</span> <span class="n">masters</span>
</span><a id="bkmonitor/api/bcs_cluster_manager/default.py-63" name="bkmonitor/api/bcs_cluster_manager/default.py-63"></a>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-64" name="bkmonitor/api/bcs_cluster_manager/default.py-64"></a>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-65" name="bkmonitor/api/bcs_cluster_manager/default.py-65"></a><span class="k">class</span> <span class="nc">FetchClustersResource</span><span class="p">(</span><span class="n">BcsClusterManagerBaseResource</span><span class="p">):</span>
<a id="bkmonitor/api/bcs_cluster_manager/default.py-66" name="bkmonitor/api/bcs_cluster_manager/default.py-66"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;从bcs-cluster-manager获取集群列表 .&quot;&quot;&quot;</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/api/bcs_helm_manager/default.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">37</span>
<span class="normal">38</span>
<span class="normal">39</span>
<span class="normal">40</span>
<span class="normal">41</span>
<span class="normal">42</span>
<span class="normal">43</span>
<span class="normal">44</span>
<span class="normal">45</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/bcs_helm_manager/default.py-37" name="bkmonitor/api/bcs_helm_manager/default.py-37"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-38" name="bkmonitor/api/bcs_helm_manager/default.py-38"></a>    <span class="n">IS_STANDARD_FORMAT</span> <span class="o">=</span> <span class="kc">False</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-39" name="bkmonitor/api/bcs_helm_manager/default.py-39"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-40" name="bkmonitor/api/bcs_helm_manager/default.py-40"></a>    <span class="k">def</span> <span class="nf">get_request_url</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">validated_request_data</span><span class="p">):</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-41" name="bkmonitor/api/bcs_helm_manager/default.py-41"></a><span class="hll">        <span class="k">return</span> <span class="p">(</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-42" name="bkmonitor/api/bcs_helm_manager/default.py-42"></a>            <span class="nb">super</span><span class="p">(</span><span class="n">BcsHelmManagerBaseResource</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-43" name="bkmonitor/api/bcs_helm_manager/default.py-43"></a>            <span class="o">.</span><span class="n">get_request_url</span><span class="p">(</span><span class="n">validated_request_data</span><span class="p">)</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-44" name="bkmonitor/api/bcs_helm_manager/default.py-44"></a>            <span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="o">**</span><span class="n">validated_request_data</span><span class="p">)</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-45" name="bkmonitor/api/bcs_helm_manager/default.py-45"></a>        <span class="p">)</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">44</span>
<span class="normal">45</span>
<span class="normal">46</span>
<span class="normal">47</span>
<span class="normal">48</span>
<span class="normal">49</span>
<span class="normal">50</span>
<span class="normal">51</span>
<span class="normal">52</span>
<span class="normal">53</span>
<span class="normal">54</span>
<span class="normal">55</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/bcs_helm_manager/default.py-44" name="bkmonitor/api/bcs_helm_manager/default.py-44"></a>            <span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="o">**</span><span class="n">validated_request_data</span><span class="p">)</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-45" name="bkmonitor/api/bcs_helm_manager/default.py-45"></a>        <span class="p">)</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-46" name="bkmonitor/api/bcs_helm_manager/default.py-46"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-47" name="bkmonitor/api/bcs_helm_manager/default.py-47"></a>    <span class="k">def</span> <span class="nf">perform_request</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">validated_request_data</span><span class="p">):</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-48" name="bkmonitor/api/bcs_helm_manager/default.py-48"></a><span class="hll">        <span class="n">result_json</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">perform_request</span><span class="p">(</span><span class="n">validated_request_data</span><span class="p">)</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-49" name="bkmonitor/api/bcs_helm_manager/default.py-49"></a><span class="hll">        <span class="n">data</span> <span class="o">=</span> <span class="n">result_json</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;data&quot;</span><span class="p">,</span> <span class="p">[])</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-50" name="bkmonitor/api/bcs_helm_manager/default.py-50"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-51" name="bkmonitor/api/bcs_helm_manager/default.py-51"></a><span class="hll">        <span class="k">return</span> <span class="n">data</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-52" name="bkmonitor/api/bcs_helm_manager/default.py-52"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-53" name="bkmonitor/api/bcs_helm_manager/default.py-53"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-54" name="bkmonitor/api/bcs_helm_manager/default.py-54"></a><span class="k">class</span> <span class="nc">FetchBkmonitorOperatorResource</span><span class="p">(</span><span class="n">BcsHelmManagerBaseResource</span><span class="p">):</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-55" name="bkmonitor/api/bcs_helm_manager/default.py-55"></a><span class="w">    </span><span class="sd">&quot;&quot;&quot;从bcs-cluster-manager获取集群列表 .&quot;&quot;&quot;</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">69</span>
<span class="normal">70</span>
<span class="normal">71</span>
<span class="normal">72</span>
<span class="normal">73</span>
<span class="normal">74</span>
<span class="normal">75</span>
<span class="normal">76</span>
<span class="normal">77</span>
<span class="normal">78</span>
<span class="normal">79</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/bcs_helm_manager/default.py-69" name="bkmonitor/api/bcs_helm_manager/default.py-69"></a>
<a id="bkmonitor/api/bcs_helm_manager/default.py-70" name="bkmonitor/api/bcs_helm_manager/default.py-70"></a><span class="sd">        Returns:</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-71" name="bkmonitor/api/bcs_helm_manager/default.py-71"></a><span class="sd">            _type_: bkmonitor-operator插件信息</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-72" name="bkmonitor/api/bcs_helm_manager/default.py-72"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/api/bcs_helm_manager/default.py-73" name="bkmonitor/api/bcs_helm_manager/default.py-73"></a><span class="hll">        <span class="n">plugins</span> <span class="o">=</span> <span class="nb">super</span><span class="p">(</span><span class="n">FetchBkmonitorOperatorResource</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="n">perform_request</span><span class="p">(</span><span class="n">params</span><span class="p">)</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-74" name="bkmonitor/api/bcs_helm_manager/default.py-74"></a><span class="hll">        <span class="k">if</span> <span class="n">plugins</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-75" name="bkmonitor/api/bcs_helm_manager/default.py-75"></a><span class="hll">            <span class="k">return</span> <span class="kc">None</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-76" name="bkmonitor/api/bcs_helm_manager/default.py-76"></a><span class="hll">        <span class="k">for</span> <span class="n">plugin</span> <span class="ow">in</span> <span class="n">plugins</span><span class="p">:</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-77" name="bkmonitor/api/bcs_helm_manager/default.py-77"></a><span class="hll">            <span class="k">if</span> <span class="n">plugin</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;bkmonitor-operator&quot;</span><span class="p">:</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-78" name="bkmonitor/api/bcs_helm_manager/default.py-78"></a><span class="hll">                <span class="k">return</span> <span class="n">plugin</span>
</span><a id="bkmonitor/api/bcs_helm_manager/default.py-79" name="bkmonitor/api/bcs_helm_manager/default.py-79"></a><span class="hll">        <span class="k">return</span> <span class="kc">None</span>
</span></pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/api/kubernetes/default.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">697</span>
<span class="normal">698</span>
<span class="normal">699</span>
<span class="normal">700</span>
<span class="normal">701</span>
<span class="normal">702</span>
<span class="normal">703</span>
<span class="normal">704</span>
<span class="normal">705</span>
<span class="normal">706</span>
<span class="normal">707</span>
<span class="normal">708</span>
<span class="normal">709</span>
<span class="normal">710</span>
<span class="normal">711</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/kubernetes/default.py-697" name="bkmonitor/api/kubernetes/default.py-697"></a><span class="sd">        Returns:</span>
<a id="bkmonitor/api/kubernetes/default.py-698" name="bkmonitor/api/kubernetes/default.py-698"></a><span class="sd">            _type_: 返回监控平台数据库存储状态</span>
<a id="bkmonitor/api/kubernetes/default.py-699" name="bkmonitor/api/kubernetes/default.py-699"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/api/kubernetes/default.py-700" name="bkmonitor/api/kubernetes/default.py-700"></a>        
<a id="bkmonitor/api/kubernetes/default.py-701" name="bkmonitor/api/kubernetes/default.py-701"></a><span class="hll">        <span class="k">if</span> <span class="n">status</span> <span class="o">==</span> <span class="s2">&quot;deployed&quot;</span><span class="p">:</span>
</span><a id="bkmonitor/api/kubernetes/default.py-702" name="bkmonitor/api/kubernetes/default.py-702"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="s2">&quot;success&quot;</span>
</span><a id="bkmonitor/api/kubernetes/default.py-703" name="bkmonitor/api/kubernetes/default.py-703"></a><span class="hll">        <span class="k">elif</span> <span class="n">status</span> <span class="o">==</span> <span class="s2">&quot;failed&quot;</span><span class="p">:</span>
</span><a id="bkmonitor/api/kubernetes/default.py-704" name="bkmonitor/api/kubernetes/default.py-704"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="s2">&quot;failed&quot;</span>
</span><a id="bkmonitor/api/kubernetes/default.py-705" name="bkmonitor/api/kubernetes/default.py-705"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/api/kubernetes/default.py-706" name="bkmonitor/api/kubernetes/default.py-706"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="s2">&quot;disabled&quot;</span>
</span><a id="bkmonitor/api/kubernetes/default.py-707" name="bkmonitor/api/kubernetes/default.py-707"></a>        
<a id="bkmonitor/api/kubernetes/default.py-708" name="bkmonitor/api/kubernetes/default.py-708"></a><span class="hll">        <span class="k">return</span> <span class="n">result</span>
</span><a id="bkmonitor/api/kubernetes/default.py-709" name="bkmonitor/api/kubernetes/default.py-709"></a>
<a id="bkmonitor/api/kubernetes/default.py-710" name="bkmonitor/api/kubernetes/default.py-710"></a>    <span class="k">def</span> <span class="nf">get_project_code</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">project_id</span><span class="p">):</span>
<a id="bkmonitor/api/kubernetes/default.py-711" name="bkmonitor/api/kubernetes/default.py-711"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;根据project_id获取project_code</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">715</span>
<span class="normal">716</span>
<span class="normal">717</span>
<span class="normal">718</span>
<span class="normal">719</span>
<span class="normal">720</span>
<span class="normal">721</span>
<span class="normal">722</span>
<span class="normal">723</span>
<span class="normal">724</span>
<span class="normal">725</span>
<span class="normal">726</span>
<span class="normal">727</span>
<span class="normal">728</span>
<span class="normal">729</span>
<span class="normal">730</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/kubernetes/default.py-715" name="bkmonitor/api/kubernetes/default.py-715"></a>
<a id="bkmonitor/api/kubernetes/default.py-716" name="bkmonitor/api/kubernetes/default.py-716"></a><span class="sd">        Returns:</span>
<a id="bkmonitor/api/kubernetes/default.py-717" name="bkmonitor/api/kubernetes/default.py-717"></a><span class="sd">            _type_: project_code</span>
<a id="bkmonitor/api/kubernetes/default.py-718" name="bkmonitor/api/kubernetes/default.py-718"></a><span class="sd">        &quot;&quot;&quot;</span>
<a id="bkmonitor/api/kubernetes/default.py-719" name="bkmonitor/api/kubernetes/default.py-719"></a><span class="hll">        <span class="n">projects</span> <span class="o">=</span> <span class="n">api</span><span class="o">.</span><span class="n">bcs_project</span><span class="o">.</span><span class="n">get_projects</span><span class="p">()</span>
</span><a id="bkmonitor/api/kubernetes/default.py-720" name="bkmonitor/api/kubernetes/default.py-720"></a><span class="hll">        <span class="k">if</span> <span class="n">projects</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
</span><a id="bkmonitor/api/kubernetes/default.py-721" name="bkmonitor/api/kubernetes/default.py-721"></a><span class="hll">            <span class="k">return</span> <span class="kc">None</span>
</span><a id="bkmonitor/api/kubernetes/default.py-722" name="bkmonitor/api/kubernetes/default.py-722"></a><span class="hll">        <span class="k">for</span> <span class="n">project</span> <span class="ow">in</span> <span class="n">projects</span><span class="p">:</span>
</span><a id="bkmonitor/api/kubernetes/default.py-723" name="bkmonitor/api/kubernetes/default.py-723"></a><span class="hll">            <span class="k">if</span> <span class="n">project</span><span class="p">[</span><span class="s2">&quot;project_id&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="n">project_id</span><span class="p">:</span>
</span><a id="bkmonitor/api/kubernetes/default.py-724" name="bkmonitor/api/kubernetes/default.py-724"></a><span class="hll">                <span class="n">project_code</span> <span class="o">=</span> <span class="n">project</span><span class="p">[</span><span class="s2">&quot;project_code&quot;</span><span class="p">]</span>
</span><a id="bkmonitor/api/kubernetes/default.py-725" name="bkmonitor/api/kubernetes/default.py-725"></a><span class="hll">                <span class="k">return</span> <span class="n">project_code</span>
</span><a id="bkmonitor/api/kubernetes/default.py-726" name="bkmonitor/api/kubernetes/default.py-726"></a><span class="hll">        <span class="k">return</span> <span class="kc">None</span>
</span><a id="bkmonitor/api/kubernetes/default.py-727" name="bkmonitor/api/kubernetes/default.py-727"></a>    
<a id="bkmonitor/api/kubernetes/default.py-728" name="bkmonitor/api/kubernetes/default.py-728"></a>    <span class="k">def</span> <span class="nf">get_clusters_from_bcs_cluster_manager</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">params</span><span class="p">):</span>
<a id="bkmonitor/api/kubernetes/default.py-729" name="bkmonitor/api/kubernetes/default.py-729"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;从cluster manager获取集群列表 .&quot;&quot;&quot;</span>
<a id="bkmonitor/api/kubernetes/default.py-730" name="bkmonitor/api/kubernetes/default.py-730"></a>        <span class="c1"># 获取集群列表</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">735</span>
<span class="normal">736</span>
<span class="normal">737</span>
<span class="normal">738</span>
<span class="normal">739</span>
<span class="normal">740</span>
<span class="normal">741</span>
<span class="normal">742</span>
<span class="normal">743</span>
<span class="normal">744</span>
<span class="normal">745</span>
<span class="normal">746</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/api/kubernetes/default.py-735" name="bkmonitor/api/kubernetes/default.py-735"></a>        <span class="n">clusters</span> <span class="o">=</span> <span class="p">[]</span>
<a id="bkmonitor/api/kubernetes/default.py-736" name="bkmonitor/api/kubernetes/default.py-736"></a>        <span class="k">for</span> <span class="n">bcs_cluster</span> <span class="ow">in</span> <span class="n">bcs_clusters</span><span class="p">:</span>
<a id="bkmonitor/api/kubernetes/default.py-737" name="bkmonitor/api/kubernetes/default.py-737"></a>            <span class="n">cluster_id</span> <span class="o">=</span> <span class="n">bcs_cluster</span><span class="p">[</span><span class="s2">&quot;clusterID&quot;</span><span class="p">]</span>
<a id="bkmonitor/api/kubernetes/default.py-738" name="bkmonitor/api/kubernetes/default.py-738"></a>            <span class="n">business_id</span> <span class="o">=</span> <span class="n">bcs_cluster</span><span class="p">[</span><span class="s2">&quot;businessID&quot;</span><span class="p">]</span>
<a id="bkmonitor/api/kubernetes/default.py-739" name="bkmonitor/api/kubernetes/default.py-739"></a><span class="hll">            <span class="n">project_code</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_project_code</span><span class="p">(</span><span class="n">bcs_cluster</span><span class="p">[</span><span class="s2">&quot;projectID&quot;</span><span class="p">])</span>
</span><a id="bkmonitor/api/kubernetes/default.py-740" name="bkmonitor/api/kubernetes/default.py-740"></a><span class="hll">            <span class="n">masters</span> <span class="o">=</span> <span class="n">api</span><span class="o">.</span><span class="n">bcs_cluster_manager</span><span class="o">.</span><span class="n">fetch_masters</span><span class="p">({</span><span class="s2">&quot;cluster_id&quot;</span><span class="p">:</span> <span class="n">cluster_id</span><span class="p">})</span>
</span><a id="bkmonitor/api/kubernetes/default.py-741" name="bkmonitor/api/kubernetes/default.py-741"></a>            
<a id="bkmonitor/api/kubernetes/default.py-742" name="bkmonitor/api/kubernetes/default.py-742"></a><span class="hll">            <span class="n">bkmonitor_operator</span> <span class="o">=</span> <span class="n">api</span><span class="o">.</span><span class="n">bcs_helm_manager</span><span class="o">.</span><span class="n">fetch_bkmonitor_operator</span><span class="p">({</span><span class="s2">&quot;cluster_id&quot;</span><span class="p">:</span> <span class="n">cluster_id</span><span class="p">,</span> <span class="s2">&quot;project_code&quot;</span><span class="p">:</span> <span class="n">project_code</span><span class="p">})</span>
</span><a id="bkmonitor/api/kubernetes/default.py-743" name="bkmonitor/api/kubernetes/default.py-743"></a>
<a id="bkmonitor/api/kubernetes/default.py-744" name="bkmonitor/api/kubernetes/default.py-744"></a>            <span class="c1"># 如果是项目空间，优先项目空间过滤</span>
<a id="bkmonitor/api/kubernetes/default.py-745" name="bkmonitor/api/kubernetes/default.py-745"></a>            <span class="k">if</span> <span class="n">project_id</span> <span class="ow">and</span> <span class="n">project_id</span> <span class="o">!=</span> <span class="n">bcs_cluster</span><span class="p">[</span><span class="s2">&quot;projectID&quot;</span><span class="p">]:</span>
<a id="bkmonitor/api/kubernetes/default.py-746" name="bkmonitor/api/kubernetes/default.py-746"></a>                <span class="k">continue</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/bkmonitor/models/bcs_cluster.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">36</span>
<span class="normal">37</span>
<span class="normal">38</span>
<span class="normal">39</span>
<span class="normal">40</span>
<span class="normal">41</span>
<span class="normal">42</span>
<span class="normal">43</span>
<span class="normal">44</span>
<span class="normal">45</span>
<span class="normal">46</span>
<span class="normal">47</span>
<span class="normal">48</span>
<span class="normal">49</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-36" name="bkmonitor/bkmonitor/models/bcs_cluster.py-36"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-37" name="bkmonitor/bkmonitor/models/bcs_cluster.py-37"></a><span class="k">class</span> <span class="nc">BCSClusterManager</span><span class="p">(</span><span class="n">BCSBaseManager</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-38" name="bkmonitor/bkmonitor/models/bcs_cluster.py-38"></a>    <span class="k">def</span> <span class="nf">count_plugin_status_quantity</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">query_set_list</span><span class="p">:</span> <span class="n">List</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-39" name="bkmonitor/bkmonitor/models/bcs_cluster.py-39"></a><span class="w">        </span><span class="sd">&quot;&quot;&quot;统计每种指标数据状态的数量 .&quot;&quot;&quot;</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-40" name="bkmonitor/bkmonitor/models/bcs_cluster.py-40"></a><span class="hll">        <span class="k">if</span> <span class="ow">not</span> <span class="n">query_set_list</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-41" name="bkmonitor/bkmonitor/models/bcs_cluster.py-41"></a><span class="hll">            <span class="n">model_items</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all</span><span class="p">()</span><span class="o">.</span><span class="n">values</span><span class="p">(</span><span class="s2">&quot;plugin_status&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">annotate</span><span class="p">(</span><span class="n">count</span><span class="o">=</span><span class="n">Count</span><span class="p">(</span><span class="s2">&quot;plugin_status&quot;</span><span class="p">))</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-42" name="bkmonitor/bkmonitor/models/bcs_cluster.py-42"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-43" name="bkmonitor/bkmonitor/models/bcs_cluster.py-43"></a><span class="hll">            <span class="n">model_items</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="o">*</span><span class="n">query_set_list</span><span class="p">)</span><span class="o">.</span><span class="n">values</span><span class="p">(</span><span class="s2">&quot;plugin_status&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">annotate</span><span class="p">(</span><span class="n">count</span><span class="o">=</span><span class="n">Count</span><span class="p">(</span><span class="s2">&quot;plugin_status&quot;</span><span class="p">))</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-44" name="bkmonitor/bkmonitor/models/bcs_cluster.py-44"></a><span class="hll">        <span class="n">status_summary</span> <span class="o">=</span> <span class="p">{</span><span class="n">model</span><span class="p">[</span><span class="s2">&quot;plugin_status&quot;</span><span class="p">]:</span> <span class="n">model</span><span class="p">[</span><span class="s2">&quot;count&quot;</span><span class="p">]</span> <span class="k">for</span> <span class="n">model</span> <span class="ow">in</span> <span class="n">model_items</span><span class="p">}</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-45" name="bkmonitor/bkmonitor/models/bcs_cluster.py-45"></a><span class="hll">        <span class="k">return</span> <span class="n">status_summary</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-46" name="bkmonitor/bkmonitor/models/bcs_cluster.py-46"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-47" name="bkmonitor/bkmonitor/models/bcs_cluster.py-47"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-48" name="bkmonitor/bkmonitor/models/bcs_cluster.py-48"></a><span class="k">class</span> <span class="nc">BCSCluster</span><span class="p">(</span><span class="n">BCSBase</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-49" name="bkmonitor/bkmonitor/models/bcs_cluster.py-49"></a>    
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">240</span>
<span class="normal">241</span>
<span class="normal">242</span>
<span class="normal">243</span>
<span class="normal">244</span>
<span class="normal">245</span>
<span class="normal">246</span>
<span class="normal">247</span>
<span class="normal">248</span>
<span class="normal">249</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-240" name="bkmonitor/bkmonitor/models/bcs_cluster.py-240"></a>            <span class="n">space_uid</span> <span class="o">=</span> <span class="n">project_id_to_space_uid</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-241" name="bkmonitor/bkmonitor/models/bcs_cluster.py-241"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-242" name="bkmonitor/bkmonitor/models/bcs_cluster.py-242"></a>            <span class="n">bcs_cluster</span> <span class="o">=</span> <span class="n">BCSCluster</span><span class="p">()</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-243" name="bkmonitor/bkmonitor/models/bcs_cluster.py-243"></a>            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">space_uid</span> <span class="o">=</span> <span class="n">space_uid</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-244" name="bkmonitor/bkmonitor/models/bcs_cluster.py-244"></a><span class="hll">            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">masters</span> <span class="o">=</span> <span class="n">c</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;masters&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-245" name="bkmonitor/bkmonitor/models/bcs_cluster.py-245"></a><span class="hll">            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">plugin_status</span> <span class="o">=</span> <span class="n">c</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;plugin_status&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-246" name="bkmonitor/bkmonitor/models/bcs_cluster.py-246"></a>            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">bk_biz_id</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">c</span><span class="p">[</span><span class="s2">&quot;bk_biz_id&quot;</span><span class="p">])</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-247" name="bkmonitor/bkmonitor/models/bcs_cluster.py-247"></a>            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">name</span> <span class="o">=</span> <span class="n">c</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-248" name="bkmonitor/bkmonitor/models/bcs_cluster.py-248"></a>            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">bcs_cluster_id</span> <span class="o">=</span> <span class="n">c</span><span class="p">[</span><span class="s2">&quot;cluster_id&quot;</span><span class="p">]</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-249" name="bkmonitor/bkmonitor/models/bcs_cluster.py-249"></a>            <span class="n">bcs_cluster</span><span class="o">.</span><span class="n">area_name</span> <span class="o">=</span> <span class="n">c</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;area_name&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>  <span class="c1"># cluster-manager 无此字段</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">340</span>
<span class="normal">341</span>
<span class="normal">342</span>
<span class="normal">343</span>
<span class="normal">344</span>
<span class="normal">345</span>
<span class="normal">346</span>
<span class="normal">347</span>
<span class="normal">348</span>
<span class="normal">349</span>
<span class="normal">350</span>
<span class="normal">351</span>
<span class="normal">352</span>
<span class="normal">353</span>
<span class="normal">354</span>
<span class="normal">355</span>
<span class="normal">356</span>
<span class="normal">357</span>
<span class="normal">358</span>
<span class="normal">359</span>
<span class="normal">360</span>
<span class="normal">361</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-340" name="bkmonitor/bkmonitor/models/bcs_cluster.py-340"></a>        <span class="k">return</span> <span class="n">result</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-341" name="bkmonitor/bkmonitor/models/bcs_cluster.py-341"></a>    
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-342" name="bkmonitor/bkmonitor/models/bcs_cluster.py-342"></a>    <span class="nd">@classmethod</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-343" name="bkmonitor/bkmonitor/models/bcs_cluster.py-343"></a>    <span class="k">def</span> <span class="nf">get_column_filter_conf</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">params</span><span class="p">,</span> <span class="n">field</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-344" name="bkmonitor/bkmonitor/models/bcs_cluster.py-344"></a><span class="hll">        <span class="n">bk_biz_id</span> <span class="o">=</span> <span class="n">params</span><span class="p">[</span><span class="s2">&quot;bk_biz_id&quot;</span><span class="p">]</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-345" name="bkmonitor/bkmonitor/models/bcs_cluster.py-345"></a><span class="hll">        <span class="n">space_related_cluster_ids</span> <span class="o">=</span> <span class="n">params</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;space_related_cluster_ids&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-346" name="bkmonitor/bkmonitor/models/bcs_cluster.py-346"></a><span class="hll">        <span class="k">if</span> <span class="n">bk_biz_id</span> <span class="o">&gt;=</span> <span class="mi">0</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-347" name="bkmonitor/bkmonitor/models/bcs_cluster.py-347"></a><span class="hll">            <span class="n">query</span> <span class="o">=</span> <span class="bp">cls</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">bk_biz_id</span><span class="o">=</span><span class="n">bk_biz_id</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-348" name="bkmonitor/bkmonitor/models/bcs_cluster.py-348"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-349" name="bkmonitor/bkmonitor/models/bcs_cluster.py-349"></a><span class="hll">            <span class="n">query</span> <span class="o">=</span> <span class="bp">cls</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">bcs_cluster_id__in</span><span class="o">=</span><span class="n">space_related_cluster_ids</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-350" name="bkmonitor/bkmonitor/models/bcs_cluster.py-350"></a><span class="hll">        <span class="n">items</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">values</span><span class="p">(</span><span class="n">field</span><span class="p">)</span><span class="o">.</span><span class="n">distinct</span><span class="p">()</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-351" name="bkmonitor/bkmonitor/models/bcs_cluster.py-351"></a><span class="hll">        <span class="n">status_list</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="nb">list</span><span class="p">({</span><span class="n">item</span><span class="p">[</span><span class="n">field</span><span class="p">]</span> <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">items</span> <span class="k">if</span> <span class="n">item</span><span class="p">[</span><span class="n">field</span><span class="p">]}))</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-352" name="bkmonitor/bkmonitor/models/bcs_cluster.py-352"></a><span class="hll">        <span class="n">plugin_status_map</span> <span class="o">=</span> <span class="p">{</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-353" name="bkmonitor/bkmonitor/models/bcs_cluster.py-353"></a>            <span class="s2">&quot;success&quot;</span><span class="p">:</span> <span class="s2">&quot;正常&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-354" name="bkmonitor/bkmonitor/models/bcs_cluster.py-354"></a>            <span class="s2">&quot;failed&quot;</span><span class="p">:</span> <span class="s2">&quot;异常&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-355" name="bkmonitor/bkmonitor/models/bcs_cluster.py-355"></a>            <span class="s2">&quot;disabled&quot;</span><span class="p">:</span> <span class="s2">&quot;未安装&quot;</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-356" name="bkmonitor/bkmonitor/models/bcs_cluster.py-356"></a>        <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-357" name="bkmonitor/bkmonitor/models/bcs_cluster.py-357"></a><span class="hll">        <span class="k">return</span> <span class="p">[</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-358" name="bkmonitor/bkmonitor/models/bcs_cluster.py-358"></a>            <span class="p">{</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-359" name="bkmonitor/bkmonitor/models/bcs_cluster.py-359"></a>                <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="n">plugin_status_map</span><span class="p">[</span><span class="n">status</span><span class="p">]</span> <span class="k">if</span> <span class="n">field</span> <span class="o">==</span> <span class="s2">&quot;plugin_status&quot;</span> <span class="k">else</span> <span class="n">status</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-360" name="bkmonitor/bkmonitor/models/bcs_cluster.py-360"></a>                <span class="s2">&quot;value&quot;</span><span class="p">:</span> <span class="n">status</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-361" name="bkmonitor/bkmonitor/models/bcs_cluster.py-361"></a>            <span class="p">}</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">364</span>
<span class="normal">365</span>
<span class="normal">366</span>
<span class="normal">367</span>
<span class="normal">368</span>
<span class="normal">369</span>
<span class="normal">370</span>
<span class="normal">371</span>
<span class="normal">372</span>
<span class="normal">373</span>
<span class="normal">374</span>
<span class="normal">375</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-364" name="bkmonitor/bkmonitor/models/bcs_cluster.py-364"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-365" name="bkmonitor/bkmonitor/models/bcs_cluster.py-365"></a>    <span class="nd">@classmethod</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-366" name="bkmonitor/bkmonitor/models/bcs_cluster.py-366"></a>    <span class="k">def</span> <span class="nf">update_plugin_status</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">params</span><span class="p">:</span> <span class="n">Dict</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-367" name="bkmonitor/bkmonitor/models/bcs_cluster.py-367"></a>        <span class="c1"># TODO 后续补充</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-368" name="bkmonitor/bkmonitor/models/bcs_cluster.py-368"></a><span class="hll">        <span class="n">bk_biz_id</span> <span class="o">=</span> <span class="n">params</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;bk_biz_id&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-369" name="bkmonitor/bkmonitor/models/bcs_cluster.py-369"></a><span class="hll">        <span class="n">bcs_cluster_id</span> <span class="o">=</span> <span class="n">params</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;bcs_cluster_id&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-370" name="bkmonitor/bkmonitor/models/bcs_cluster.py-370"></a><span class="hll">        <span class="n">plugin_status</span> <span class="o">=</span> <span class="s2">&quot;success&quot;</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-371" name="bkmonitor/bkmonitor/models/bcs_cluster.py-371"></a><span class="hll">        <span class="bp">cls</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">bcs_cluster_id</span><span class="o">=</span><span class="n">bcs_cluster_id</span><span class="p">)</span><span class="o">.</span><span class="n">update</span><span class="p">(</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-372" name="bkmonitor/bkmonitor/models/bcs_cluster.py-372"></a>            <span class="o">**</span><span class="p">{</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-373" name="bkmonitor/bkmonitor/models/bcs_cluster.py-373"></a>                <span class="s2">&quot;plugin_status&quot;</span><span class="p">:</span> <span class="n">plugin_status</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-374" name="bkmonitor/bkmonitor/models/bcs_cluster.py-374"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-375" name="bkmonitor/bkmonitor/models/bcs_cluster.py-375"></a>        <span class="p">)</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">403</span>
<span class="normal">404</span>
<span class="normal">405</span>
<span class="normal">406</span>
<span class="normal">407</span>
<span class="normal">408</span>
<span class="normal">409</span>
<span class="normal">410</span>
<span class="normal">411</span>
<span class="normal">412</span>
<span class="normal">413</span>
<span class="normal">414</span>
<span class="normal">415</span>
<span class="normal">416</span>
<span class="normal">417</span>
<span class="normal">418</span>
<span class="normal">419</span>
<span class="normal">420</span>
<span class="normal">421</span>
<span class="normal">422</span>
<span class="normal">423</span>
<span class="normal">424</span>
<span class="normal">425</span>
<span class="normal">426</span>
<span class="normal">427</span>
<span class="normal">428</span>
<span class="normal">429</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-403" name="bkmonitor/bkmonitor/models/bcs_cluster.py-403"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-404" name="bkmonitor/bkmonitor/models/bcs_cluster.py-404"></a>        <span class="p">)</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-405" name="bkmonitor/bkmonitor/models/bcs_cluster.py-405"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-406" name="bkmonitor/bkmonitor/models/bcs_cluster.py-406"></a>    <span class="k">def</span> <span class="nf">render_bcs_cluster_id</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bk_biz_id</span><span class="p">,</span> <span class="n">render_type</span><span class="o">=</span><span class="s2">&quot;list&quot;</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-407" name="bkmonitor/bkmonitor/models/bcs_cluster.py-407"></a><span class="hll">        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">bcs_cluster_id</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-408" name="bkmonitor/bkmonitor/models/bcs_cluster.py-408"></a>    
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-409" name="bkmonitor/bkmonitor/models/bcs_cluster.py-409"></a>    <span class="k">def</span> <span class="nf">render_plugin_status</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bk_biz_id</span><span class="p">,</span> <span class="n">render_type</span><span class="o">=</span><span class="s2">&quot;list&quot;</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-410" name="bkmonitor/bkmonitor/models/bcs_cluster.py-410"></a><span class="hll">        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">plugin_status</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">METRICS_STATE_FAILURE</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-411" name="bkmonitor/bkmonitor/models/bcs_cluster.py-411"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-412" name="bkmonitor/bkmonitor/models/bcs_cluster.py-412"></a>                <span class="s2">&quot;type&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">METRICS_STATE_FAILURE</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-413" name="bkmonitor/bkmonitor/models/bcs_cluster.py-413"></a>                <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="n">_</span><span class="p">(</span><span class="s2">&quot;异常&quot;</span><span class="p">),</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-414" name="bkmonitor/bkmonitor/models/bcs_cluster.py-414"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-415" name="bkmonitor/bkmonitor/models/bcs_cluster.py-415"></a><span class="hll">        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">plugin_status</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">METRICS_STATE_STATE_SUCCESS</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-416" name="bkmonitor/bkmonitor/models/bcs_cluster.py-416"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-417" name="bkmonitor/bkmonitor/models/bcs_cluster.py-417"></a>                <span class="s2">&quot;type&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">METRICS_STATE_STATE_SUCCESS</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-418" name="bkmonitor/bkmonitor/models/bcs_cluster.py-418"></a>                <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="n">_</span><span class="p">(</span><span class="s2">&quot;正常&quot;</span><span class="p">),</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-419" name="bkmonitor/bkmonitor/models/bcs_cluster.py-419"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-420" name="bkmonitor/bkmonitor/models/bcs_cluster.py-420"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-421" name="bkmonitor/bkmonitor/models/bcs_cluster.py-421"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-422" name="bkmonitor/bkmonitor/models/bcs_cluster.py-422"></a>                <span class="s2">&quot;type&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">METRICS_STATE_DISABLED</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-423" name="bkmonitor/bkmonitor/models/bcs_cluster.py-423"></a>                <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="n">_</span><span class="p">(</span><span class="s2">&quot;未安装&quot;</span><span class="p">),</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-424" name="bkmonitor/bkmonitor/models/bcs_cluster.py-424"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-425" name="bkmonitor/bkmonitor/models/bcs_cluster.py-425"></a><span class="hll">        <span class="k">return</span> <span class="n">result</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-426" name="bkmonitor/bkmonitor/models/bcs_cluster.py-426"></a>    
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-427" name="bkmonitor/bkmonitor/models/bcs_cluster.py-427"></a>    <span class="k">def</span> <span class="nf">render_bcs_cluster</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bk_biz_id</span><span class="p">,</span> <span class="n">render_type</span><span class="o">=</span><span class="s2">&quot;list&quot;</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-428" name="bkmonitor/bkmonitor/models/bcs_cluster.py-428"></a>        <span class="k">if</span> <span class="n">render_type</span> <span class="o">==</span> <span class="s2">&quot;list&quot;</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-429" name="bkmonitor/bkmonitor/models/bcs_cluster.py-429"></a>            <span class="k">return</span> <span class="p">{</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">432</span>
<span class="normal">433</span>
<span class="normal">434</span>
<span class="normal">435</span>
<span class="normal">436</span>
<span class="normal">437</span>
<span class="normal">438</span>
<span class="normal">439</span>
<span class="normal">440</span>
<span class="normal">441</span>
<span class="normal">442</span>
<span class="normal">443</span>
<span class="normal">444</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-432" name="bkmonitor/bkmonitor/models/bcs_cluster.py-432"></a>                <span class="s2">&quot;key&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-433" name="bkmonitor/bkmonitor/models/bcs_cluster.py-433"></a>                <span class="s2">&quot;url&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-434" name="bkmonitor/bkmonitor/models/bcs_cluster.py-434"></a>                <span class="s2">&quot;value&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">(</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">bcs_cluster_id</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-435" name="bkmonitor/bkmonitor/models/bcs_cluster.py-435"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-436" name="bkmonitor/bkmonitor/models/bcs_cluster.py-436"></a><span class="hll">        <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">(</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">bcs_cluster_id</span><span class="si">}</span><span class="s2">)&quot;</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-437" name="bkmonitor/bkmonitor/models/bcs_cluster.py-437"></a>    
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-438" name="bkmonitor/bkmonitor/models/bcs_cluster.py-438"></a>    <span class="k">def</span> <span class="nf">render_bcs_masters</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bk_biz_id</span><span class="p">,</span> <span class="n">render_type</span><span class="o">=</span><span class="s2">&quot;list&quot;</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-439" name="bkmonitor/bkmonitor/models/bcs_cluster.py-439"></a><span class="hll">        <span class="k">if</span> <span class="n">render_type</span> <span class="o">==</span> <span class="s2">&quot;list&quot;</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-440" name="bkmonitor/bkmonitor/models/bcs_cluster.py-440"></a><span class="hll">            <span class="k">return</span> <span class="p">{</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-441" name="bkmonitor/bkmonitor/models/bcs_cluster.py-441"></a>                <span class="s2">&quot;icon&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-442" name="bkmonitor/bkmonitor/models/bcs_cluster.py-442"></a>                <span class="s2">&quot;target&quot;</span><span class="p">:</span> <span class="s2">&quot;null_event&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-443" name="bkmonitor/bkmonitor/models/bcs_cluster.py-443"></a>                <span class="s2">&quot;key&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-444" name="bkmonitor/bkmonitor/models/bcs_cluster.py-444"></a>                <span class="s2">&quot;url&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">443</span>
<span class="normal">444</span>
<span class="normal">445</span>
<span class="normal">446</span>
<span class="normal">447</span>
<span class="normal">448</span>
<span class="normal">449</span>
<span class="normal">450</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-443" name="bkmonitor/bkmonitor/models/bcs_cluster.py-443"></a>                <span class="s2">&quot;key&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-444" name="bkmonitor/bkmonitor/models/bcs_cluster.py-444"></a>                <span class="s2">&quot;url&quot;</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-445" name="bkmonitor/bkmonitor/models/bcs_cluster.py-445"></a>                <span class="s2">&quot;value&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">masters</span><span class="p">,</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-446" name="bkmonitor/bkmonitor/models/bcs_cluster.py-446"></a>            <span class="p">}</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-447" name="bkmonitor/bkmonitor/models/bcs_cluster.py-447"></a><span class="hll">        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">masters</span>
</span><a id="bkmonitor/bkmonitor/models/bcs_cluster.py-448" name="bkmonitor/bkmonitor/models/bcs_cluster.py-448"></a>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-449" name="bkmonitor/bkmonitor/models/bcs_cluster.py-449"></a>    <span class="k">def</span> <span class="nf">render_cpu_usage_ratio</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bk_biz_id</span><span class="p">,</span> <span class="n">render_type</span><span class="o">=</span><span class="s2">&quot;list&quot;</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/models/bcs_cluster.py-450" name="bkmonitor/bkmonitor/models/bcs_cluster.py-450"></a>        <span class="k">return</span> <span class="n">get_progress_value</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">cpu_usage_ratio</span><span class="p">)</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/bkmonitor/utils/cipher.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">127</span>
<span class="normal">128</span>
<span class="normal">129</span>
<span class="normal">130</span>
<span class="normal">131</span>
<span class="normal">132</span>
<span class="normal">133</span>
<span class="normal">134</span>
<span class="normal">135</span>
<span class="normal">136</span>
<span class="normal">137</span>
<span class="normal">138</span>
<span class="normal">139</span>
<span class="normal">140</span>
<span class="normal">141</span>
<span class="normal">142</span>
<span class="normal">143</span>
<span class="normal">144</span>
<span class="normal">145</span>
<span class="normal">146</span>
<span class="normal">147</span>
<span class="normal">148</span>
<span class="normal">149</span>
<span class="normal">150</span>
<span class="normal">151</span>
<span class="normal">152</span>
<span class="normal">153</span>
<span class="normal">154</span>
<span class="normal">155</span>
<span class="normal">156</span>
<span class="normal">157</span>
<span class="normal">158</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/cipher.py-127" name="bkmonitor/bkmonitor/utils/cipher.py-127"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-128" name="bkmonitor/bkmonitor/utils/cipher.py-128"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-129" name="bkmonitor/bkmonitor/utils/cipher.py-129"></a><span class="k">class</span> <span class="nc">AESGCMCipher</span><span class="p">(</span><span class="nb">object</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-130" name="bkmonitor/bkmonitor/utils/cipher.py-130"></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-131" name="bkmonitor/bkmonitor/utils/cipher.py-131"></a><span class="hll">        <span class="bp">self</span><span class="o">.</span><span class="n">key</span> <span class="o">=</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">sha256</span><span class="p">(</span><span class="n">key</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">))</span><span class="o">.</span><span class="n">digest</span><span class="p">()</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-132" name="bkmonitor/bkmonitor/utils/cipher.py-132"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-133" name="bkmonitor/bkmonitor/utils/cipher.py-133"></a>    <span class="k">def</span> <span class="nf">encrypt</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">plain_text</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-134" name="bkmonitor/bkmonitor/utils/cipher.py-134"></a><span class="hll">        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plain_text</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-135" name="bkmonitor/bkmonitor/utils/cipher.py-135"></a><span class="hll">            <span class="n">plain_text</span> <span class="o">=</span> <span class="n">plain_text</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-136" name="bkmonitor/bkmonitor/utils/cipher.py-136"></a>        <span class="c1"># 生成随机的nonce</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-137" name="bkmonitor/bkmonitor/utils/cipher.py-137"></a><span class="hll">        <span class="n">nonce</span> <span class="o">=</span> <span class="n">get_random_bytes</span><span class="p">(</span><span class="mi">12</span><span class="p">)</span>  <span class="c1"># GCM模式通常使用12字节的nonce</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-138" name="bkmonitor/bkmonitor/utils/cipher.py-138"></a>        <span class="c1"># 创建AES-GCM加密器实例</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-139" name="bkmonitor/bkmonitor/utils/cipher.py-139"></a><span class="hll">        <span class="n">cipher</span> <span class="o">=</span> <span class="n">AES</span><span class="o">.</span><span class="n">new</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">key</span><span class="p">,</span> <span class="n">AES</span><span class="o">.</span><span class="n">MODE_GCM</span><span class="p">,</span> <span class="n">nonce</span><span class="o">=</span><span class="n">nonce</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-140" name="bkmonitor/bkmonitor/utils/cipher.py-140"></a>        <span class="c1"># 加密数据</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-141" name="bkmonitor/bkmonitor/utils/cipher.py-141"></a><span class="hll">        <span class="n">cipher_text</span><span class="p">,</span> <span class="n">tag</span> <span class="o">=</span> <span class="n">cipher</span><span class="o">.</span><span class="n">encrypt_and_digest</span><span class="p">(</span><span class="n">plain_text</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-142" name="bkmonitor/bkmonitor/utils/cipher.py-142"></a><span class="hll">        <span class="n">result</span> <span class="o">=</span> <span class="n">nonce</span> <span class="o">+</span> <span class="n">cipher_text</span> <span class="o">+</span> <span class="n">tag</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-143" name="bkmonitor/bkmonitor/utils/cipher.py-143"></a><span class="hll">        <span class="n">result_base64</span> <span class="o">=</span> <span class="n">base64</span><span class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-144" name="bkmonitor/bkmonitor/utils/cipher.py-144"></a><span class="hll">        <span class="k">return</span> <span class="n">result_base64</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-145" name="bkmonitor/bkmonitor/utils/cipher.py-145"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-146" name="bkmonitor/bkmonitor/utils/cipher.py-146"></a>    <span class="k">def</span> <span class="nf">decrypt</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cipher_text</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-147" name="bkmonitor/bkmonitor/utils/cipher.py-147"></a><span class="hll">        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">cipher_text</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-148" name="bkmonitor/bkmonitor/utils/cipher.py-148"></a><span class="hll">            <span class="n">cipher_text</span> <span class="o">=</span> <span class="n">cipher_text</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-149" name="bkmonitor/bkmonitor/utils/cipher.py-149"></a><span class="hll">        <span class="n">content</span> <span class="o">=</span> <span class="n">base64</span><span class="o">.</span><span class="n">b64decode</span><span class="p">(</span><span class="n">cipher_text</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-150" name="bkmonitor/bkmonitor/utils/cipher.py-150"></a><span class="hll">        <span class="n">nonce</span> <span class="o">=</span> <span class="n">content</span><span class="p">[:</span><span class="mi">12</span><span class="p">]</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-151" name="bkmonitor/bkmonitor/utils/cipher.py-151"></a><span class="hll">        <span class="n">cipher_text</span> <span class="o">=</span> <span class="n">content</span><span class="p">[</span><span class="mi">12</span><span class="p">:</span><span class="o">-</span><span class="mi">16</span><span class="p">]</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-152" name="bkmonitor/bkmonitor/utils/cipher.py-152"></a><span class="hll">        <span class="n">tag</span> <span class="o">=</span> <span class="n">content</span><span class="p">[</span><span class="o">-</span><span class="mi">16</span><span class="p">:]</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-153" name="bkmonitor/bkmonitor/utils/cipher.py-153"></a><span class="hll">        <span class="n">cipher</span> <span class="o">=</span> <span class="n">AES</span><span class="o">.</span><span class="n">new</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">key</span><span class="p">,</span> <span class="n">AES</span><span class="o">.</span><span class="n">MODE_GCM</span><span class="p">,</span> <span class="n">nonce</span><span class="o">=</span><span class="n">nonce</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-154" name="bkmonitor/bkmonitor/utils/cipher.py-154"></a><span class="hll">        <span class="n">plain_text</span> <span class="o">=</span> <span class="n">cipher</span><span class="o">.</span><span class="n">decrypt_and_verify</span><span class="p">(</span><span class="n">cipher_text</span><span class="p">,</span> <span class="n">tag</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-155" name="bkmonitor/bkmonitor/utils/cipher.py-155"></a><span class="hll">        <span class="k">return</span> <span class="n">plain_text</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-156" name="bkmonitor/bkmonitor/utils/cipher.py-156"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-157" name="bkmonitor/bkmonitor/utils/cipher.py-157"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-158" name="bkmonitor/bkmonitor/utils/cipher.py-158"></a><span class="k">class</span> <span class="nc">SM4</span><span class="p">:</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">231</span>
<span class="normal">232</span>
<span class="normal">233</span>
<span class="normal">234</span>
<span class="normal">235</span>
<span class="normal">236</span>
<span class="normal">237</span>
<span class="normal">238</span>
<span class="normal">239</span>
<span class="normal">240</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/cipher.py-231" name="bkmonitor/bkmonitor/utils/cipher.py-231"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-232" name="bkmonitor/bkmonitor/utils/cipher.py-232"></a><span class="k">def</span> <span class="nf">encrypt_by_aes</span><span class="p">(</span><span class="n">encrypt_message</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-233" name="bkmonitor/bkmonitor/utils/cipher.py-233"></a>    <span class="c1"># 获取逻辑等同 bkmonitor.utils.db.fields.get_key_config</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-234" name="bkmonitor/bkmonitor/utils/cipher.py-234"></a>    <span class="n">key</span> <span class="o">=</span> <span class="n">get_aes_key</span><span class="p">()</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-235" name="bkmonitor/bkmonitor/utils/cipher.py-235"></a><span class="hll">    <span class="n">cipher</span> <span class="o">=</span> <span class="n">AESGCMCipher</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-236" name="bkmonitor/bkmonitor/utils/cipher.py-236"></a><span class="hll">    <span class="k">return</span> <span class="n">cipher</span><span class="o">.</span><span class="n">encrypt</span><span class="p">(</span><span class="n">encrypt_message</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-237" name="bkmonitor/bkmonitor/utils/cipher.py-237"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-238" name="bkmonitor/bkmonitor/utils/cipher.py-238"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-239" name="bkmonitor/bkmonitor/utils/cipher.py-239"></a><span class="k">def</span> <span class="nf">decrypt_by_aes</span><span class="p">(</span><span class="n">decrypt_message</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-240" name="bkmonitor/bkmonitor/utils/cipher.py-240"></a>    <span class="c1"># 获取逻辑等同 bkmonitor.utils.db.fields.get_key_config</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">240</span>
<span class="normal">241</span>
<span class="normal">242</span>
<span class="normal">243</span>
<span class="normal">244</span>
<span class="normal">245</span>
<span class="normal">246</span>
<span class="normal">247</span>
<span class="normal">248</span>
<span class="normal">249</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/cipher.py-240" name="bkmonitor/bkmonitor/utils/cipher.py-240"></a>    <span class="c1"># 获取逻辑等同 bkmonitor.utils.db.fields.get_key_config</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-241" name="bkmonitor/bkmonitor/utils/cipher.py-241"></a>    <span class="k">if</span> <span class="n">decrypt_message</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">decrypt_message</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-242" name="bkmonitor/bkmonitor/utils/cipher.py-242"></a>        <span class="k">return</span> <span class="s1">&#39;&#39;</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-243" name="bkmonitor/bkmonitor/utils/cipher.py-243"></a>    <span class="n">key</span> <span class="o">=</span> <span class="n">get_aes_key</span><span class="p">()</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-244" name="bkmonitor/bkmonitor/utils/cipher.py-244"></a><span class="hll">    <span class="n">cipher</span> <span class="o">=</span> <span class="n">AESGCMCipher</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-245" name="bkmonitor/bkmonitor/utils/cipher.py-245"></a><span class="hll">    <span class="k">return</span> <span class="n">cipher</span><span class="o">.</span><span class="n">decrypt</span><span class="p">(</span><span class="n">decrypt_message</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/cipher.py-246" name="bkmonitor/bkmonitor/utils/cipher.py-246"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-247" name="bkmonitor/bkmonitor/utils/cipher.py-247"></a>
<a id="bkmonitor/bkmonitor/utils/cipher.py-248" name="bkmonitor/bkmonitor/utils/cipher.py-248"></a><span class="k">def</span> <span class="nf">get_aes_key</span><span class="p">():</span>
<a id="bkmonitor/bkmonitor/utils/cipher.py-249" name="bkmonitor/bkmonitor/utils/cipher.py-249"></a>    <span class="c1"># 获取配置的AES加解密秘钥</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/bkmonitor/utils/consul.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">65</span>
<span class="normal">66</span>
<span class="normal">67</span>
<span class="normal">68</span>
<span class="normal">69</span>
<span class="normal">70</span>
<span class="normal">71</span>
<span class="normal">72</span>
<span class="normal">73</span>
<span class="normal">74</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/consul.py-65" name="bkmonitor/bkmonitor/utils/consul.py-65"></a>        <span class="n">client_cert</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">(</span><span class="s2">&quot;CONSUL_CLIENT_CERT_FILE&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-66" name="bkmonitor/bkmonitor/utils/consul.py-66"></a>        <span class="n">client_key</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">(</span><span class="s2">&quot;CONSUL_CLIENT_KEY_FILE&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-67" name="bkmonitor/bkmonitor/utils/consul.py-67"></a>        <span class="n">server_cert</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">(</span><span class="s2">&quot;CONSUL_SERVER_CA_CERT&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-68" name="bkmonitor/bkmonitor/utils/consul.py-68"></a>        <span class="n">https_port</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">(</span><span class="s2">&quot;CONSUL_HTTPS_PORT&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-69" name="bkmonitor/bkmonitor/utils/consul.py-69"></a><span class="hll">        <span class="n">token</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">(</span><span class="s2">&quot;CONSUL_TOKEN&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/consul.py-70" name="bkmonitor/bkmonitor/utils/consul.py-70"></a><span class="hll">        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;consul token is </span><span class="si">{</span><span class="n">token</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/consul.py-71" name="bkmonitor/bkmonitor/utils/consul.py-71"></a>        <span class="n">client_cert</span> <span class="o">=</span> <span class="n">client_cert</span> <span class="k">if</span> <span class="n">is_file_exists</span><span class="p">(</span><span class="n">client_cert</span><span class="p">)</span> <span class="k">else</span> <span class="kc">None</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-72" name="bkmonitor/bkmonitor/utils/consul.py-72"></a>        <span class="n">client_key</span> <span class="o">=</span> <span class="n">client_key</span> <span class="k">if</span> <span class="n">is_file_exists</span><span class="p">(</span><span class="n">client_key</span><span class="p">)</span> <span class="k">else</span> <span class="kc">None</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-73" name="bkmonitor/bkmonitor/utils/consul.py-73"></a>        <span class="n">server_cert</span> <span class="o">=</span> <span class="n">server_cert</span> <span class="k">if</span> <span class="n">is_file_exists</span><span class="p">(</span><span class="n">server_cert</span><span class="p">)</span> <span class="k">else</span> <span class="kc">None</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-74" name="bkmonitor/bkmonitor/utils/consul.py-74"></a>        <span class="n">https_port</span> <span class="o">=</span> <span class="n">https_port</span> <span class="k">if</span> <span class="n">https_port</span> <span class="k">else</span> <span class="kc">None</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">91</span>
<span class="normal">92</span>
<span class="normal">93</span>
<span class="normal">94</span>
<span class="normal">95</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/consul.py-91" name="bkmonitor/bkmonitor/utils/consul.py-91"></a>            <span class="c1"># 需要转换为 `localhost`</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-92" name="bkmonitor/bkmonitor/utils/consul.py-92"></a>            <span class="k">if</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;host&quot;</span><span class="p">)</span> <span class="o">==</span> <span class="s2">&quot;127.0.0.1&quot;</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-93" name="bkmonitor/bkmonitor/utils/consul.py-93"></a>                <span class="n">kwargs</span><span class="p">[</span><span class="s2">&quot;host&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;localhost&quot;</span>
<a id="bkmonitor/bkmonitor/utils/consul.py-94" name="bkmonitor/bkmonitor/utils/consul.py-94"></a>
<a id="bkmonitor/bkmonitor/utils/consul.py-95" name="bkmonitor/bkmonitor/utils/consul.py-95"></a><span class="hll">        <span class="nb">super</span><span class="p">(</span><span class="n">BKConsul</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">scheme</span><span class="o">=</span><span class="n">scheme</span><span class="p">,</span> <span class="n">verify</span><span class="o">=</span><span class="n">verify</span><span class="p">,</span> <span class="n">cert</span><span class="o">=</span><span class="n">cert</span><span class="p">,</span> <span class="n">port</span><span class="o">=</span><span class="n">port</span><span class="p">,</span> <span class="n">token</span><span class="o">=</span><span class="n">token</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
</span></pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/bkmonitor/utils/rsa/crypto.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">11</span>
<span class="normal">12</span>
<span class="normal">13</span>
<span class="normal">14</span>
<span class="normal">15</span>
<span class="normal">16</span>
<span class="normal">17</span>
<span class="normal">18</span>
<span class="normal">19</span>
<span class="normal">20</span>
<span class="normal">21</span>
<span class="normal">22</span>
<span class="normal">23</span>
<span class="normal">24</span>
<span class="normal">25</span>
<span class="normal">26</span>
<span class="normal">27</span>
<span class="normal">28</span>
<span class="normal">29</span>
<span class="normal">30</span>
<span class="normal">31</span>
<span class="normal">32</span>
<span class="normal">33</span>
<span class="normal">34</span>
<span class="normal">35</span>
<span class="normal">36</span>
<span class="normal">37</span>
<span class="normal">38</span>
<span class="normal">39</span>
<span class="normal">40</span>
<span class="normal">41</span>
<span class="normal">42</span>
<span class="normal">43</span>
<span class="normal">44</span>
<span class="normal">45</span>
<span class="normal">46</span>
<span class="normal">47</span>
<span class="normal">48</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-11" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-11"></a><span class="k">class</span> <span class="nc">SM4CryptoUtil</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-12" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-12"></a>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">sm4_key</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-13" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-13"></a>        <span class="c1"># 确保 sm4_key 是字节类型</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-14" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-14"></a>        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">sm4_key</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-15" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-15"></a><span class="hll">            <span class="bp">self</span><span class="o">.</span><span class="n">sm4_key</span> <span class="o">=</span> <span class="n">sm4_key</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-16" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-16"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-17" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-17"></a>            <span class="bp">self</span><span class="o">.</span><span class="n">sm4_key</span> <span class="o">=</span> <span class="n">sm4_key</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-18" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-18"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-19" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-19"></a>    <span class="k">def</span> <span class="nf">sm4_encrypt</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">plaintext</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-20" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-20"></a><span class="hll">        <span class="k">try</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-21" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-21"></a>            <span class="c1"># 将明文转换为字节</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-22" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-22"></a><span class="hll">            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">plaintext</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-23" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-23"></a><span class="hll">                <span class="n">data</span> <span class="o">=</span> <span class="n">plaintext</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-24" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-24"></a>            <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-25" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-25"></a><span class="hll">                <span class="n">data</span> <span class="o">=</span> <span class="n">plaintext</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-26" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-26"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-27" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-27"></a><span class="hll">            <span class="n">block_size</span> <span class="o">=</span> <span class="mi">16</span>  <span class="c1"># SM4的块大小是16字节</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-28" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-28"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-29" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-29"></a>            <span class="c1"># 生成随机IV</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-30" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-30"></a><span class="hll">            <span class="n">iv</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">urandom</span><span class="p">(</span><span class="n">block_size</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-31" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-31"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-32" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-32"></a>            <span class="c1"># 创建加密器</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-33" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-33"></a><span class="hll">            <span class="n">crypt_sm4</span> <span class="o">=</span> <span class="n">CryptSM4</span><span class="p">()</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-34" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-34"></a><span class="hll">            <span class="n">crypt_sm4</span><span class="o">.</span><span class="n">set_key</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sm4_key</span><span class="p">,</span> <span class="n">SM4_ENCRYPT</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-35" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-35"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-36" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-36"></a>            <span class="c1"># 加密</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-37" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-37"></a><span class="hll">            <span class="n">encrypted_data</span> <span class="o">=</span> <span class="n">crypt_sm4</span><span class="o">.</span><span class="n">crypt_cbc</span><span class="p">(</span><span class="n">iv</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-38" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-38"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-39" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-39"></a>            <span class="c1"># 组合IV和密文，并进行base64编码</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-40" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-40"></a><span class="hll">            <span class="n">result</span> <span class="o">=</span> <span class="n">iv</span> <span class="o">+</span> <span class="n">encrypted_data</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-41" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-41"></a><span class="hll">            <span class="k">return</span> <span class="n">base64</span><span class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">result</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-42" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-42"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-43" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-43"></a><span class="hll">        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-44" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-44"></a><span class="hll">            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;加密失败: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-45" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-45"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-46" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-46"></a>    <span class="k">def</span> <span class="nf">sm4_decrypt</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ciphertext</span><span class="p">):</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-47" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-47"></a>        <span class="k">if</span> <span class="ow">not</span> <span class="n">ciphertext</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-48" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-48"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;内容为 None&quot;</span><span class="p">)</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">50</span>
<span class="normal">51</span>
<span class="normal">52</span>
<span class="normal">53</span>
<span class="normal">54</span>
<span class="normal">55</span>
<span class="normal">56</span>
<span class="normal">57</span>
<span class="normal">58</span>
<span class="normal">59</span>
<span class="normal">60</span>
<span class="normal">61</span>
<span class="normal">62</span>
<span class="normal">63</span>
<span class="normal">64</span>
<span class="normal">65</span>
<span class="normal">66</span>
<span class="normal">67</span>
<span class="normal">68</span>
<span class="normal">69</span>
<span class="normal">70</span>
<span class="normal">71</span>
<span class="normal">72</span>
<span class="normal">73</span>
<span class="normal">74</span>
<span class="normal">75</span>
<span class="normal">76</span>
<span class="normal">77</span>
<span class="normal">78</span>
<span class="normal">79</span>
<span class="normal">80</span>
<span class="normal">81</span>
<span class="normal">82</span>
<span class="normal">83</span>
<span class="normal">84</span>
<span class="normal">85</span>
<span class="normal">86</span>
<span class="normal">87</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-50" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-50"></a>        <span class="n">mark</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;CMSS_CRYPTO_MARK&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-51" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-51"></a>        <span class="n">pattern</span> <span class="o">=</span> <span class="sa">r</span><span class="s1">&#39;</span><span class="si">{}</span><span class="s1">\(.*?\)&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">mark</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-52" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-52"></a>        <span class="n">match</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">pattern</span><span class="p">,</span> <span class="n">ciphertext</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-53" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-53"></a>        <span class="k">if</span> <span class="n">match</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-54" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-54"></a><span class="hll">            <span class="n">ciphertext</span> <span class="o">=</span> <span class="n">match</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">0</span><span class="p">)[</span><span class="nb">len</span><span class="p">(</span><span class="n">mark</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span><span class="p">:</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-55" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-55"></a>        <span class="k">else</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-56" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-56"></a>            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;密文缺少标志位&quot;</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-57" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-57"></a>            <span class="k">return</span> <span class="n">ciphertext</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-58" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-58"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-59" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-59"></a><span class="hll">        <span class="k">try</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-60" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-60"></a>            <span class="c1"># base64解码</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-61" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-61"></a><span class="hll">            <span class="n">data</span> <span class="o">=</span> <span class="n">base64</span><span class="o">.</span><span class="n">b64decode</span><span class="p">(</span><span class="n">ciphertext</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-62" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-62"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-63" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-63"></a>            <span class="c1"># 检查数据长度</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-64" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-64"></a><span class="hll">            <span class="n">block_size</span> <span class="o">=</span> <span class="mi">16</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-65" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-65"></a><span class="hll">            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">block_size</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-66" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-66"></a><span class="hll">                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;密文太短&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-67" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-67"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-68" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-68"></a>            <span class="c1"># 提取IV和密文</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-69" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-69"></a><span class="hll">            <span class="n">iv</span> <span class="o">=</span> <span class="n">data</span><span class="p">[:</span><span class="n">block_size</span><span class="p">]</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-70" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-70"></a><span class="hll">            <span class="n">encrypted_data</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">block_size</span><span class="p">:]</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-71" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-71"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-72" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-72"></a>            <span class="c1"># 创建解密器</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-73" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-73"></a><span class="hll">            <span class="n">crypt_sm4</span> <span class="o">=</span> <span class="n">CryptSM4</span><span class="p">()</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-74" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-74"></a><span class="hll">            <span class="n">crypt_sm4</span><span class="o">.</span><span class="n">set_key</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sm4_key</span><span class="p">,</span> <span class="n">SM4_DECRYPT</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-75" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-75"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-76" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-76"></a>            <span class="c1"># 解密</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-77" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-77"></a><span class="hll">            <span class="n">decrypted_data</span> <span class="o">=</span> <span class="n">crypt_sm4</span><span class="o">.</span><span class="n">crypt_cbc</span><span class="p">(</span><span class="n">iv</span><span class="p">,</span> <span class="n">encrypted_data</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-78" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-78"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-79" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-79"></a><span class="hll">            <span class="k">return</span> <span class="n">decrypted_data</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-80" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-80"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-81" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-81"></a><span class="hll">        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-82" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-82"></a><span class="hll">            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;解密失败: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-83" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-83"></a><span class="hll">            <span class="k">return</span> <span class="n">ciphertext</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-84" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-84"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-85" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-85"></a><span class="n">crypto_util</span> <span class="o">=</span> <span class="n">SM4CryptoUtil</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;CMSS_CRYPTO_KEY&quot;</span><span class="p">))</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-86" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-86"></a>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-87" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-87"></a><span class="k">def</span> <span class="nf">decrypt_environment_variables</span><span class="p">():</span>
</pre></div></td></tr></table></div>

            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">90</span>
<span class="normal">91</span>
<span class="normal">92</span>
<span class="normal">93</span>
<span class="normal">94</span>
<span class="normal">95</span>
<span class="normal">96</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-90" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-90"></a>        <span class="n">encrypted_value</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-91" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-91"></a>        <span class="k">try</span><span class="p">:</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-92" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-92"></a>            <span class="n">decrypted</span> <span class="o">=</span> <span class="n">crypto_util</span><span class="o">.</span><span class="n">sm4_decrypt</span><span class="p">(</span><span class="n">encrypted_value</span><span class="p">)</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-93" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-93"></a>            <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">decrypted</span>
<a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-94" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-94"></a><span class="hll">        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-95" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-95"></a><span class="hll">            <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error decrypting </span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</span><a id="bkmonitor/bkmonitor/utils/rsa/crypto.py-96" name="bkmonitor/bkmonitor/utils/rsa/crypto.py-96"></a><span class="hll">            <span class="k">continue</span>
</span></pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/config/tools/elasticsearch.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">26</span>
<span class="normal">27</span>
<span class="normal">28</span>
<span class="normal">29</span>
<span class="normal">30</span>
<span class="normal">31</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/config/tools/elasticsearch.py-26" name="bkmonitor/config/tools/elasticsearch.py-26"></a>        <span class="n">host</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;BK_MONITOR_ES7_HOST&quot;</span><span class="p">,</span> <span class="s2">&quot;es7.service.consul&quot;</span><span class="p">)</span>
<a id="bkmonitor/config/tools/elasticsearch.py-27" name="bkmonitor/config/tools/elasticsearch.py-27"></a>        <span class="n">rest_port</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;BK_MONITOR_ES7_REST_PORT&quot;</span><span class="p">,</span> <span class="s2">&quot;9200&quot;</span><span class="p">))</span>
<a id="bkmonitor/config/tools/elasticsearch.py-28" name="bkmonitor/config/tools/elasticsearch.py-28"></a>        <span class="n">transport_port</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;BK_MONITOR_ES7_TRANSPORT_PORT&quot;</span><span class="p">,</span> <span class="s2">&quot;9301&quot;</span><span class="p">))</span>
<a id="bkmonitor/config/tools/elasticsearch.py-29" name="bkmonitor/config/tools/elasticsearch.py-29"></a>        <span class="n">user</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;BK_MONITOR_ES7_USER&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<a id="bkmonitor/config/tools/elasticsearch.py-30" name="bkmonitor/config/tools/elasticsearch.py-30"></a><span class="hll">        <span class="n">password</span> <span class="o">=</span> <span class="n">crypto_util</span><span class="o">.</span><span class="n">sm4_decrypt</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;BK_MONITOR_ES7_PASSWORD&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">))</span>
</span><a id="bkmonitor/config/tools/elasticsearch.py-31" name="bkmonitor/config/tools/elasticsearch.py-31"></a>    <span class="k">return</span> <span class="n">host</span><span class="p">,</span> <span class="n">rest_port</span><span class="p">,</span> <span class="n">transport_port</span><span class="p">,</span> <span class="n">user</span><span class="p">,</span> <span class="n">password</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/config/tools/rabbitmq.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">37</span>
<span class="normal">38</span>
<span class="normal">39</span>
<span class="normal">40</span>
<span class="normal">41</span>
<span class="normal">42</span>
<span class="normal">43</span>
<span class="normal">44</span>
<span class="normal">45</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/config/tools/rabbitmq.py-37" name="bkmonitor/config/tools/rabbitmq.py-37"></a>        <span class="n">vhost</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;RABBITMQ_VHOST&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<a id="bkmonitor/config/tools/rabbitmq.py-38" name="bkmonitor/config/tools/rabbitmq.py-38"></a>        <span class="n">port</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;RABBITMQ_PORT&quot;</span><span class="p">,</span> <span class="mi">5672</span><span class="p">))</span>
<a id="bkmonitor/config/tools/rabbitmq.py-39" name="bkmonitor/config/tools/rabbitmq.py-39"></a>        <span class="n">host</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;RABBITMQ_HOST&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<a id="bkmonitor/config/tools/rabbitmq.py-40" name="bkmonitor/config/tools/rabbitmq.py-40"></a>        <span class="n">user</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;RABBITMQ_USER&quot;</span><span class="p">,</span> <span class="s2">&quot;guest&quot;</span><span class="p">)</span>
<a id="bkmonitor/config/tools/rabbitmq.py-41" name="bkmonitor/config/tools/rabbitmq.py-41"></a><span class="hll">        <span class="n">password</span> <span class="o">=</span> <span class="n">crypto_util</span><span class="o">.</span><span class="n">sm4_decrypt</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">getenv</span><span class="p">(</span><span class="s2">&quot;RABBITMQ_PASSWORD&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">))</span>
</span><a id="bkmonitor/config/tools/rabbitmq.py-42" name="bkmonitor/config/tools/rabbitmq.py-42"></a>
<a id="bkmonitor/config/tools/rabbitmq.py-43" name="bkmonitor/config/tools/rabbitmq.py-43"></a>    <span class="n">broker_url</span> <span class="o">=</span> <span class="s2">&quot;amqp://</span><span class="si">{user}</span><span class="s2">:</span><span class="si">{password}</span><span class="s2">@</span><span class="si">{host}</span><span class="s2">:</span><span class="si">{port}</span><span class="s2">/</span><span class="si">{vhost}</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
<a id="bkmonitor/config/tools/rabbitmq.py-44" name="bkmonitor/config/tools/rabbitmq.py-44"></a>        <span class="n">user</span><span class="o">=</span><span class="n">user</span><span class="p">,</span> <span class="n">password</span><span class="o">=</span><span class="n">password</span><span class="p">,</span> <span class="n">host</span><span class="o">=</span><span class="n">host</span><span class="p">,</span> <span class="n">port</span><span class="o">=</span><span class="n">port</span><span class="p">,</span> <span class="n">vhost</span><span class="o">=</span><span class="n">vhost</span>
<a id="bkmonitor/config/tools/rabbitmq.py-45" name="bkmonitor/config/tools/rabbitmq.py-45"></a>    <span class="p">)</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
        <div class="src-snippet">
            <div class="src-name">bkmonitor/packages/utils/redis_client.py</div>
            <div class="snippets">
            <div class="snippet"><table class="snippettable"><tr><td class="linenos"><div class="linenodiv"><pre><span class="normal">64</span>
<span class="normal">65</span>
<span class="normal">66</span>
<span class="normal">67</span>
<span class="normal">68</span>
<span class="normal">69</span>
<span class="normal">70</span>
<span class="normal">71</span>
<span class="normal">72</span></pre></div></td><td class="code"><div><pre><span></span><a id="bkmonitor/packages/utils/redis_client.py-64" name="bkmonitor/packages/utils/redis_client.py-64"></a>                <span class="p">],</span>
<a id="bkmonitor/packages/utils/redis_client.py-65" name="bkmonitor/packages/utils/redis_client.py-65"></a>                <span class="s2">&quot;sentinel_kwargs&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="n">password</span><span class="p">},</span>
<a id="bkmonitor/packages/utils/redis_client.py-66" name="bkmonitor/packages/utils/redis_client.py-66"></a>            <span class="p">}</span>
<a id="bkmonitor/packages/utils/redis_client.py-67" name="bkmonitor/packages/utils/redis_client.py-67"></a>            <span class="n">host</span><span class="p">,</span> <span class="n">port</span> <span class="o">=</span> <span class="n">Sentinel</span><span class="p">(</span><span class="o">**</span><span class="n">sentinel_params</span><span class="p">)</span><span class="o">.</span><span class="n">discover_master</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">prefix</span><span class="si">}</span><span class="s2">_REDIS_SENTINEL_MASTER_NAME&quot;</span><span class="p">])</span>
<a id="bkmonitor/packages/utils/redis_client.py-68" name="bkmonitor/packages/utils/redis_client.py-68"></a><span class="hll">            <span class="n">redis_password</span> <span class="o">=</span> <span class="n">crypto_util</span><span class="o">.</span><span class="n">sm4_decrypt</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">prefix</span><span class="si">}</span><span class="s2">_REDIS_PASSWORD&quot;</span><span class="p">])</span>
</span><a id="bkmonitor/packages/utils/redis_client.py-69" name="bkmonitor/packages/utils/redis_client.py-69"></a>            <span class="n">configs</span> <span class="o">=</span> <span class="p">{</span>
<a id="bkmonitor/packages/utils/redis_client.py-70" name="bkmonitor/packages/utils/redis_client.py-70"></a>                <span class="s2">&quot;host&quot;</span><span class="p">:</span> <span class="n">host</span><span class="p">,</span>
<a id="bkmonitor/packages/utils/redis_client.py-71" name="bkmonitor/packages/utils/redis_client.py-71"></a>                <span class="s2">&quot;port&quot;</span><span class="p">:</span> <span class="n">port</span><span class="p">,</span>
<a id="bkmonitor/packages/utils/redis_client.py-72" name="bkmonitor/packages/utils/redis_client.py-72"></a>                <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="n">redis_password</span><span class="p">,</span>
</pre></div></td></tr></table></div>

            </div>
        </div>
    </body>
</html>