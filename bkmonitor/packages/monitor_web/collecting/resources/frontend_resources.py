# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""
import time
import csv
import logging
from functools import partial

from django.utils.translation import ugettext as _
from django.http import HttpResponse, HttpResponseServerError
from django.db import models, transaction
from monitor_web.collecting.constant import CollectStatus, OperationType, ExportConfigKey
from monitor_web.commons.cc.utils import foreach_topo_tree
from monitor_web.commons.data_access import ResultTable
from monitor_web.models.custom_report import CustomEventGroup
from monitor_web.models.collecting import CollectConfigMeta, DeploymentConfigVersion, ExportConfig
from monitor_web.models.plugin import PluginVersionHistory
from monitor_web.plugin.manager import PluginManagerFactory
from monitor_web.plugin.constant import PluginType
from bkmonitor.data_source import BkMonitorLogDataSource
from bkmonitor.commons.tools import get_host_view_display_fields
from bkmonitor.views import serializers
from constants.cmdb import TargetNodeType
from core.drf_resource import resource, api
from core.drf_resource.base import Resource
from core.errors.collecting import CollectConfigNotExist

from utils.query_data import TSDataBase

logger = logging.getLogger(__name__)


class FrontendCollectConfigDetailResource(Resource):
    """
    获取采集配置详细信息，供前端展示用
    """

    class RequestSerializer(serializers.Serializer):
        id = serializers.IntegerField(required=True, label="采集配置ID")

    def perform_request(self, params):
        config_detail = resource.collecting.collect_config_detail(id=params["id"])

        # 基本信息
        basic_info = {
            "name": config_detail["name"],
            "target_object_type": config_detail["target_object_type"],  # SERVICE, HOST
            "collect_type": config_detail["collect_type"],
            "plugin_display_name": config_detail["plugin_info"]["plugin_display_name"],
            "plugin_id": config_detail["plugin_info"]["plugin_id"],
            "period": config_detail["params"]["collector"]["period"],
            "bk_biz_id": config_detail["bk_biz_id"],
            "label_info": config_detail["label_info"],
            "create_time": config_detail["create_time"],
            "create_user": config_detail["create_user"],
            "update_time": config_detail["update_time"],
            "update_user": config_detail["update_user"],
        }

        # 运行参数
        runtime_params = []
        config_json = config_detail["plugin_info"]["config_json"]
        if config_detail["collect_type"] == PluginType.SNMP:
            for key, item in enumerate(config_json):
                if item.get("auth_json"):
                    config_json.extend(config_json.pop(key).pop("auth_json"))
                    break
        for item in config_json:
            if item["mode"] != "collector":
                item["mode"] = "plugin"
            runtime_params.append(
                {
                    "name": item["description"] or item["name"],
                    "value": config_detail["params"][item["mode"]][item["name"]],
                    "type": item["type"],
                }
            )

        # 指标预览
        metric_list = []
        for item in config_detail["plugin_info"]["metric_json"]:
            field_list = []
            for field in item["fields"]:
                field_list.append(
                    {
                        "metric": field["monitor_type"],
                        "englishName": field["name"],
                        "aliaName": field["description"],
                        "type": field["type"],
                        "unit": field["unit"],
                    }
                )
            metric_list.append(
                {
                    "id": item["table_name"],
                    "name": item["table_desc"],
                    "list": field_list,
                    "table_id": item["table_id"],
                }
            )

        # 采集目标
        table_data = []
        if config_detail["target_node_type"] == TargetNodeType.INSTANCE:
            for item in config_detail["target"]:
                table_data.append(
                    {
                        "display_name": item["display_name"],
                        "bk_host_id": item["bk_host_id"],
                        "ip": item["ip"],
                        "agent_status": item["agent_status"],
                        "bk_cloud_name": item["bk_cloud_name"],
                    }
                )
        elif config_detail["target_node_type"] in [TargetNodeType.SET_TEMPLATE, TargetNodeType.SERVICE_TEMPLATE]:
            template_ids = [target["bk_inst_id"] for target in config_detail["target"]]
            nodes = resource.commons.get_nodes_by_template(
                bk_biz_id=config_detail["bk_biz_id"],
                bk_obj_id=config_detail["target_node_type"],
                bk_inst_ids=template_ids,
                bk_inst_type=config_detail["target_object_type"],
            )
            for item in nodes:
                table_data.append(
                    {"bk_inst_name": item["bk_inst_name"], "count": item["count"], "labels": item["labels"]}
                )
        else:
            for item in config_detail["target"]:
                table_data.append(
                    {"bk_inst_name": item["bk_inst_name"], "count": item["count"], "labels": item["labels"]}
                )

        target_info = {"target_node_type": config_detail["target_node_type"], "table_data": table_data}

        result = {
            "basic_info": basic_info,
            "runtime_params": runtime_params,
            "metric_list": metric_list,
            "target_info": target_info,
            "subscription_id": config_detail["subscription_id"],
            "extend_info": config_detail["params"],
        }
        return result


class FrontendTargetStatusTopoResource(Resource):
    """
    获取检查视图页左侧topo树
    """

    class RequestSerializer(serializers.Serializer):
        bk_biz_id = serializers.IntegerField(required=True, label="业务ID")
        id = serializers.IntegerField(required=True, label="采集配置ID")
        only_instance = serializers.BooleanField(label="是否值显示实例列表", default=False)

    @staticmethod
    def handle_node(bk_biz_id: int, node, node_link=None):
        """
        拓扑节点处理
        """
        field, alias_field = get_host_view_display_fields(bk_biz_id)
        # 子节点处理
        child = node.pop("child", [])
        if child:
            node["children"] = []
        for item in child:
            # 去除不包含实例的节点
            if item.get("children") or item.get("bk_host_id"):
                node["children"].append(item)

        # 补充字段
        if node.get("service_instance_id"):
            node["name"] = node.get("instance_name")
            node["status"] = node.get("status", CollectStatus.FAILED)
            node["id"] = node["service_instance_id"]
        elif node.get("bk_host_id"):
            node["name"] = node.get("instance_name")
            node["alias_name"] = node.get(alias_field, "")
            node["id"] = str(node["bk_host_id"])
            node["status"] = node.get("status", CollectStatus.FAILED)
        elif node.get("bk_inst_name"):
            node["name"] = node["bk_inst_name"]
            node["id"] = f"{node['bk_obj_id']}|{node['bk_inst_id']}"
        else:
            node["name"] = _("无法识别节点")

    def perform_request(self, params):
        topo_tree = resource.collecting.collect_target_status_topo(params)
        config = CollectConfigMeta.objects.select_related("deployment_config").get(id=params["id"])

        handle_node = partial(self.handle_node, params["bk_biz_id"])

        # 实例处理
        if config.deployment_config.target_node_type == TargetNodeType.INSTANCE:
            for node in topo_tree:
                handle_node(node, None)
            return topo_tree

        # 动态拓扑处理
        foreach_topo_tree(topo_tree, handle_node, order="desc")

        # 补充目标字段
        if params["only_instance"]:
            queue = [topo_tree]
            while queue:
                node = queue.pop()
                if "service_instance_id" in node:
                    node["target"] = {"bk_target_service_instance_id": node["service_instance_id"]}
                elif "bk_host_id" in node:
                    node["target"] = {
                        "bk_target_ip": node["ip"],
                        "bk_target_cloud_id": node["bk_cloud_id"],
                        "bk_host_id": node["bk_host_id"],
                    }
                queue.extend(node.get("children", []))

        return [topo for topo in topo_tree if topo.get("children")]


class DeploymentConfigDiffResource(Resource):
    """
    用于列表页重新进入执行中的采集配置
    """

    class RequestSerializer(serializers.Serializer):
        id = serializers.IntegerField(required=True, label="采集配置id")

    def perform_request(self, validated_request_data):
        try:
            collect_config = CollectConfigMeta.objects.get(id=validated_request_data["id"])
        except CollectConfigMeta.DoesNotExist:
            raise CollectConfigNotExist({"msg": validated_request_data["id"]})

        # 获得采集配置的上一份部署配置
        if collect_config.last_operation == OperationType.ROLLBACK:
            last_version = DeploymentConfigVersion.objects.filter(parent_id=collect_config.deployment_config.id).last()
        else:
            last_version = collect_config.deployment_config.last_version

        # 返回两份配置的diff_node差异信息
        if last_version:
            diff_node = last_version.show_diff(collect_config.deployment_config)["nodes"]
        else:
            diff_node = {
                "is_modified": True,
                "added": collect_config.deployment_config.target_nodes,
                "updated": [],
                "removed": [],
                "unchanged": [],
            }
        return diff_node


class GetCollectVariablesResource(Resource):
    def perform_request(self, validated_request_data):
        data = [
            ["{{ target.host.bk_host_innerip }}", _("主机内网IP"), "127.0.0.1"],
            ["{{ target.host.bk_cloud_id }}", _("主机云区域ID"), "0"],
            ["{{ target.host.bk_cloud_name }}", _("主机云区域名称"), "default area"],
            ["{{ target.host.bk_host_id }}", _("主机ID"), "1"],
            ["{{ target.host.operator }}", _("主机负责人"), "user1,user2"],
            ["{{ target.host.bk_bak_operator }}", _("主机备份负责人"), "user1,user2"],
            ["{{ target.host.bk_host_name }}", _("主机名"), "VM_centos"],
            ["{{ target.host.bk_isp_name }}", _("ISP名称"), _("联通")],
            ["{{ target.host.bk_os_name }}", _("操作系统名称"), "linux centos"],
            ["{{ target.host.bk_os_version }}", _("操作系统版本"), "7.4.1700"],
            ["{{ target.service.id }}", _("服务实例ID"), "1"],
            ["{{ target.service.name }}", _("服务实例名称"), "test"],
            ["{{ target.service.bk_module_id }}", _("模块ID"), "1"],
            ["{{ target.service.bk_host_id }}", _("主机ID"), "1"],
            ["{{ target.service.service_category_id }}", _("服务分类ID"), "1"],
            ['{{ target.service.labels["label_name"] }}', _("标签"), "test"],
            ['{{ target.process["process_name"].bk_process_id }}', _("进程ID"), "1"],
            ['{{ target.process["process_name"].bk_process_name }}', _("进程别名"), "1"],
            ['{{ target.process["process_name"].bk_func_name }}', _("进程名称"), "1"],
            ['{{ target.process["process_name"].bind_info[index].port }}', _("进程端口"), "80,81-85"],
            ['{{ target.process["process_name"].bind_info[index].ip }}', _("绑定IP"), "127.0.0.1"],
            ['{{ target.process["process_name"].bk_func_id }}', _("功能ID"), "123"],
            ['{{ target.process["process_name"].user }}', _("启动用户"), "root"],
            ['{{ target.process["process_name"].work_path }}', _("工作路径"), "/data/bkee"],
            ['{{ target.process["process_name"].proc_num }}', _("进程数量"), "4"],
            ['{{ target.process["process_name"].pid_file }}', _("PID文件路径"), "/data/bkee/a.pid"],
            ['{{ target.process["process_name"].auto_start }}', _("自动启动"), "false"],
        ]
        return [{"name": record[0], "description": record[1], "example": record[2]} for record in data]


class HostStatusExportResource(Resource):
    """
    导出采集下发所有主机状态
    """

    class RequestSerializer(serializers.Serializer):
        bk_biz_id = serializers.IntegerField(required=True, label="业务ID")
        id = serializers.IntegerField(required=True, label="采集配置ID")

    @staticmethod
    def get_hosts_by_topo(bk_biz_id: int, target_params: list):
        """
        根据拓扑节点查找主机列表
        """
        topo_nodes = {}
        for target in target_params:
            bk_obj_id = target.get("bk_obj_id")
            bk_inst_id = target.get("bk_inst_id")
            if bk_obj_id in topo_nodes:
                topo_nodes[bk_obj_id].extend(bk_inst_id)
            else:
                topo_nodes[bk_obj_id] = [bk_inst_id]
        if len(topo_nodes) > 0:
            return api.cmdb.get_host_by_topo_node(bk_biz_id=bk_biz_id, topo_nodes=topo_nodes)
        return None

    @staticmethod
    def get_hosts_by_template(bk_biz_id: int, template_type, target_params: list):
        """
        根据服务模板、集群模板查找主机列表
        """
        template_ids = []
        for target in target_params:
            template_ids.append(target.get("bk_inst_id"))
        if len(template_ids) > 0:
            return api.cmdb.get_host_by_template(bk_biz_id=bk_biz_id, bk_obj_id=template_type, template_ids=template_ids)
        return None

    def nodata_test(self, target_list, config):
        if not target_list:
            return []

        # 取3个采集周期内的数据，若3个采集周期都无数据则判断为无数据
        period = config.deployment_config.params["collector"]["period"]

        filter_dict = {"bk_collect_config_id": str(config.id)}

        # 日志关键字无数据判断
        if config.plugin.plugin_type == PluginType.LOG or config.plugin.plugin_type == PluginType.SNMP_TRAP:
            version = config.deployment_config.plugin_version
            event_group_name = "{}_{}".format(version.plugin.plugin_type, version.plugin_id)
            group_info = CustomEventGroup.objects.get(name=event_group_name)

            if "bk_target_ip" in target_list[0]:
                group_by = ["bk_target_ip", "bk_target_cloud_id"]
            else:
                group_by = ["bk_target_service_instance_id"]

            data_source = BkMonitorLogDataSource(
                table=group_info.table_id,
                group_by=group_by,
                metrics=[{"field": "_index", "method": "COUNT"}],
                filter_dict=filter_dict,
            )
            records = data_source.query_data(start_time=int(time.time()) * 1000 - period * 3000)
            has_data_targets = set()
            for record in records:
                has_data_targets.add("|".join(str(record[field]) for field in group_by))

            new_target_list = []
            for target in target_list:
                key = "|".join(str(target[field]) for field in group_by)
                new_target = {"no_data": key not in has_data_targets}
                new_target.update(target)
                new_target_list.append(new_target)

            return new_target_list
        else:
            is_split_measurement = False
            if config.plugin.is_split_measurement:
                # 是单指标单表的模式
                is_split_measurement = True
                db_name = f"{config.plugin.plugin_type}_{config.plugin.plugin_id}".lower()
                group_result = api.metadata.query_time_series_group(bk_biz_id=0, time_series_group_name=db_name)
                result_tables = [ResultTable.time_series_group_to_result_table(group_result)]
            else:
                # 获取结果表配置
                if config.plugin.plugin_type == PluginType.PROCESS:
                    db_name = "process"
                    metric_json = PluginManagerFactory.get_manager(
                        plugin=config.plugin.plugin_id, plugin_type=config.plugin.plugin_type
                    ).gen_metric_info()

                    metric_json = [table for table in metric_json if table["table_name"] == "perf"]
                else:
                    db_name = "{plugin_type}_{plugin_id}".format(
                        plugin_type=config.plugin.plugin_type, plugin_id=config.plugin.plugin_id
                    )
                    latest_info_version = self.fetch_latest_version_by_config(config)
                    metric_json = latest_info_version.info.metric_json
                result_tables = [ResultTable.new_result_table(table) for table in metric_json]
            if period < 60:
                filter_dict["time__gt"] = f"{period * 3 // 60 + 1}m"
            else:
                filter_dict["time__gt"] = f"{period // 60 * 3}m"
            ts_database = TSDataBase(db_name=db_name, result_tables=result_tables, bk_biz_id=config.bk_biz_id)
            result = ts_database.no_data_test(
                test_target_list=target_list, filter_dict=filter_dict, is_split_measurement=is_split_measurement
            )
            return result

    def fetch_latest_version_by_config(self, config):
        """
        根据主配置拿最新子配置版本号
        """
        config_version = self.config.deployment_config.plugin_version.config_version
        latest_info_version = PluginVersionHistory.objects.filter(
            plugin=self.config.plugin, config_version=config_version, stage=PluginVersionHistory.Stage.RELEASE
        ).latest("info_version")
        return latest_info_version

    def perform_request(self, params):
        max_limit_size = ExportConfig.objects.filter(key=ExportConfigKey.MAX_LIMIT_SIZE)
        if not max_limit_size:
            logger.error("缺少配置：同时最大可导出数量【MAX_LIMIT_SIZE】")
            return HttpResponseServerError("缺少配置：同时最大可导出数量")
        max_size = max_limit_size[0].value
        real_time_export_count = ExportConfig.objects.filter(key=ExportConfigKey.REAL_TIME_EXPORT_COUNT)
        if not real_time_export_count:
            logger.error("缺少配置：当前正在导出数量【REAL_TIME_EXPORT_COUNT】")
            return HttpResponseServerError("缺少配置：当前正在导出数量")
        count = real_time_export_count[0].value
        if count >= max_size:
            return HttpResponseServerError("当前正在导出的数量已达到限制值，请稍后再导出。")

        with transaction.atomic():
            ExportConfig.objects.filter(key=ExportConfigKey.REAL_TIME_EXPORT_COUNT).update(value=models.F('value') + 1)

        try:
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="采集下发主机列表.csv"'

            writer = csv.writer(response)
            writer.writerow(['ip', '状态'])

            # 获取全量ip列表
            bk_biz_id = params["bk_biz_id"]
            config = CollectConfigMeta.objects.select_related("deployment_config").get(id=params["id"])
            target_node_type = config.deployment_config.target_node_type
            target_params = config.deployment_config.target_nodes

            ips = {}
            if target_node_type == TargetNodeType.INSTANCE:
                # 前后期版本数据库存储数据格式不一致，以订阅id从节点处获取
                pass
            elif target_node_type in [TargetNodeType.SERVICE_TEMPLATE, TargetNodeType.SET_TEMPLATE]:
                hosts = self.get_hosts_by_template(bk_biz_id, target_node_type, target_params)
                if hosts:
                    for host in hosts:
                        ips[host.display_name + "|" + str(host.bk_cloud_id)] = ""
            elif target_node_type == TargetNodeType.TOPO:
                hosts = self.get_hosts_by_topo(bk_biz_id, target_params)
                if hosts:
                    for host in hosts:
                        ips[host.display_name + "|" + str(host.bk_cloud_id)] = ""
            else:
                logger.error(f"不支持该下发目标类型: {target_node_type}")
                return HttpResponseServerError("采集下发目标类型错误，无法统计主机ip信息。")

            # 根据订阅id 查看下发结果
            ips_success = []
            subscription_id = config.deployment_config.subscription_id
            status_result = api.node_man.batch_task_result(subscription_id=subscription_id, need_detail=False)
            for instance in status_result:
                ip_v4 = instance["instance_info"]["host"]["bk_host_innerip"]
                ip_v6 = instance["instance_info"]["host"].get("bk_host_innerip_v6", "")
                bk_cloud_id = int(instance["instance_info"]["host"]["bk_cloud_id"])
                status = instance["status"]
                ip = ip_v6 if not ip_v4 else ip_v4
                ips[ip + "|" + str(bk_cloud_id)] = status
                if status == CollectStatus.SUCCESS:
                    ips_success.append({"bk_target_ip": ip, "bk_target_cloud_id": bk_cloud_id})

            ips_normal_data_set = set()
            for item in self.nodata_test(ips_success, config):
                if not item["no_data"]:
                    ips_normal_data_set.add(item["bk_target_ip"])

            for data in ips_success:
                if data["bk_target_ip"] not in ips_normal_data_set:
                    ips[data["bk_target_ip"] + "|" + str(data["bk_target_cloud_id"])] = CollectStatus.NODATA

            for key, value in ips.items():
                ip = key.split("|")[0]
                if value == CollectStatus.SUCCESS:
                    writer.writerow([ip, "正常"])
                elif value == CollectStatus.FAILED:
                    writer.writerow([ip, "异常"])
                elif value == CollectStatus.NODATA:
                    writer.writerow([ip, "无数据"])
                elif value == CollectStatus.PENDING or value == CollectStatus.RUNNING:
                    writer.writerow([ip, "部署中"])
                else:
                    writer.writerow([ip, "未知"])

            return response
        except Exception as e:
            logger.error(f"采集主机状态导出发生异常: {e}")
            return HttpResponseServerError("导出后端服务发生异常。")
        finally:
            with transaction.atomic():
                ExportConfig.objects.filter(key=ExportConfigKey.REAL_TIME_EXPORT_COUNT).update(
                    value=models.F('value') - 1)
