# AIGC 
import unittest
from unittest.mock import patch
from bkmonitor.views import serializers
from bkmonitor.utils.user import get_global_user
from core.drf_resource.base import Resource
from core.errors.common import DataError
from core.drf_resource import api, resource
from django.db import transaction
from django.db.models import Q
from monitor_web.tasks import (
    deploy_collecting_config,
    deploy_collecting,
    delete_collecting,
    toggle_collecting
)
from monitor_web.models.base_monitor import (
    BaseCollectorConfigMeta,
    BaseCollectorViewMeta
)
from monitor_web.models.plugin import CollectorPluginMeta
from ..constant import (
    CONFIG_DEPLOY_TYPE,
    COLLECTING_CHOICE,
    STATUS_VIEW_MAPPING
)
from monitor_web.models.collecting import CollectConfigMeta
from monitor_web.constants import BASE_COLLECTING_CHINESE

class TestCreateCollectingConfigResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = CreateCollectingConfigResource()
    
    def test_perform_request_when_valid_data_should_create_template(self):
        # Arrange
        validated_request_data = {
            "collector_name": "TestCollector",
            "collector_type": "type1",
            "collector_label": "label1",
            "component_name": "component1",
            "collector_plugin": "plugin1",
            "params": {"key": "value"}
        }
        
        # Act & Assert
        with self.assertLogs(level='INFO') as log:
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result, "创建base采集模版成功")
            self.assertIn("创建base采集模版成功", log.output[0])
    
    def test_perform_request_when_duplicate_data_should_raise_error(self):
        # Arrange
        validated_request_data = {
            "collector_name": "DuplicateCollector",
            "collector_type": "type1",
            "collector_label": "label1",
            "component_name": "component1",
            "collector_plugin": "plugin1",
            "params": {"key": "value"}
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.create', side_effect=Exception("Duplicate entry")):
            with self.assertRaises(DataError) as context:
                self.resource.perform_request(validated_request_data)
            self.assertTrue("不能插入重复模版" in str(context.exception))

class TestEditCollectingConfigResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = EditCollectingConfigResource()
    
    def test_perform_request_when_valid_data_should_update_template(self):
        # Arrange
        validated_request_data = {
            "parent_id": 1,
            "collector_name": "UpdatedCollector",
            "params": {"key": "newValue"}
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.get', return_value=BaseCollectorConfigMeta()):
            with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
                result = self.resource.perform_request(validated_request_data)
                self.assertEqual(result, [])
    
    def test_perform_request_when_invalid_id_should_return_failure(self):
        # Arrange
        validated_request_data = {
            "parent_id": 999,
            "collector_name": "UpdatedCollector",
            "params": {"key": "newValue"}
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.get', side_effect=BaseCollectorConfigMeta.DoesNotExist):
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result, "edit failed")

class TestDeployCollectingConfigResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = DeployCollectingConfigResource()
    
    def test_perform_request_when_valid_data_should_return_task_id(self):
        # Arrange
        validated_request_data = {
            "parent_ids": [1, 2],
            "spaces": [{"space_id": "id1", "space_name": "name1"}],
            "deploy_type": "normal"
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
            with patch('deploy_collecting_config.delay', return_value="mocked_task_id"):
                result = self.resource.perform_request(validated_request_data)
                self.assertEqual(result, "mocked_task_id")
    
    def test_perform_request_when_exception_occurs_should_return_none(self):
        # Arrange
        validated_request_data = {
            "parent_ids": [1, 2],
            "spaces": [{"space_id": "id1", "space_name": "name1"}],
            "deploy_type": "normal"
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', side_effect=Exception("Database error")):
            result = self.resource.perform_request(validated_request_data)
            self.assertIsNone(result)

class TestBaseCollectingListResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = BaseCollectingListResource()
    
    def test_perform_request_when_valid_params_should_return_data(self):
        # Arrange
        params = {
            "page": 1,
            "page_size": 10,
            "search": {
                "component_name": "component1",
                "collector_name": "collector1",
                "status": "active"
            }
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
            result = self.resource.perform_request(params)
            self.assertEqual(result["count"], 0)
            self.assertEqual(len(result["data"]), 0)
    
    def test_perform_request_when_empty_search_should_return_all_data(self):
        # Arrange
        params = {
            "page": 1,
            "page_size": 10,
            "search": {}
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
            result = self.resource.perform_request(params)
            self.assertEqual(result["count"], 0)
            self.assertEqual(len(result["data"]), 0)

class TestSaveConfigResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = SaveConfigResource()
    
    def test_perform_request_when_valid_data_should_return_params(self):
        # Arrange
        validated_request_data = {
            "parent_id": 1,
            "space_id": "id1"
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[BaseCollectorViewMeta()]):
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result["base_id"], 1)
            self.assertEqual(result["bk_biz_id"], "id1")
    
    def test_perform_request_when_no_config_should_raise_error(self):
        # Arrange
        validated_request_data = {
            "parent_id": 999,
            "space_id": "id1"
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
            with self.assertRaises(DataError) as context:
                self.resource.perform_request(validated_request_data)
            self.assertTrue("该父模版在该业务下下发失败或其他未知错误" in str(context.exception))

class TestDeploySaveConfigResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = DeploySaveConfigResource()
    
    def test_perform_request_when_valid_data_should_return_task_ids(self):
        # Arrange
        validated_request_data = {
            "parent_ids": [1, 2],
            "deploy_type": "normal"
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
            with patch('resource.base_monitor.save_config', return_value={}):
                with patch('resource.collecting.save_collect_config', return_value="mocked_result"):
                    result = self.resource.perform_request(validated_request_data)
                    self.assertEqual(result, {"1": "mocked_result", "2": "mocked_result"})
    
    def test_perform_request_when_exception_occurs_should_return_partial_results(self):
        # Arrange
        validated_request_data = {
            "parent_ids": [1, 2],
            "deploy_type": "normal"
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', side_effect=Exception("Database error")):
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result, {})

class TestDeployBaseCollectingResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = DeployBaseCollectingResource()
    
    def test_perform_request_when_valid_data_should_return_task_id(self):
        # Arrange
        validated_request_data = {
            "task_id": "task123",
            "parent_ids": [1, 2],
            "deploy_type": "normal"
        }
        
        # Act & Assert
        with patch('deploy_collecting.delay', return_value="mocked_task_id"):
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result, "mocked_task_id")

class TestDeleteCollectingResource(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.resource = DeleteCollectingResource()
    
    def test_perform_request_when_valid_data_should_delete_template(self):
        # Arrange
        validated_request_data = {
            "parent_id": 1,
            "deploy_ids": [1, 2]
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', return_value=[]):
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result, [])
    
    def test_perform_request_when_invalid_id_should_return_failure(self):
        # Arrange
        validated_request_data = {
            "parent_id": 999,
            "deploy_ids": [1, 2]
        }
        
        # Act & Assert
        with patch('core.drf_resource.base.Resource.objects.filter', side_effect=BaseCollectorConfigMeta.DoesNotExist):
            result = self.resource.perform_request(validated_request_data)
            self.assertEqual(result, "delete failed")

if __name__ == "__main__":
    unittest.main()