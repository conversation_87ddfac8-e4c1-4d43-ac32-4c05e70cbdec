/* eslint-disable codecc/comment-ratio */
/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台 (BlueKing PaaS) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台 (BlueKing PaaS) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台 (BlueKing PaaS):
 *
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 */

// 表单 label 相关的词条
export default {
  // 规范：驼峰

  隐藏已停用: 'Hide deactivated', // 查看位置 语义不明
  周期内连续探测: 'Continuous Detection Within the Cycle',
  明确排除通知方式: 'Explicitly exclude notification methods',
  分派人员: 'Dispatch Person',
  通知人员: 'Receivers',
  操作者: 'Operator',
  所有者: 'Owner',
  订阅人: 'Subscriber',
  主要维护人: 'Primary Maintainer',
  备份维护人: 'Backup Maintainer',
  交接时间: 'Handover Time',
  工作周期: 'Work Cycle',
  工作时间: 'Work Time',
  生效时间: 'Effective Time',
  轮值时间: 'Rotation Time',
  生效时间段: 'Effective Period',
  时间范围: 'Time Range',
  通知时间: 'Notification Time',
  异常时间: 'Exception Time',
  时间: 'Time',
  事件时间: 'Event Time',
  过期时间: 'Expire Time',
  副本数: 'Replicas',
  分片数: 'Shards',
  索引切分大小: 'Split Size',
  存储索引名: 'Storage Index Name',
  时间区间: 'Time Range',
  服务数: 'Services',
  层级数: 'Layers',
  span总数: 'Spans',
  维度信息: 'Dimension',
  告警产生时间: 'Generation Time',
  告警类型: 'Alarm Type',
  关联信息: 'Related',
  执行状态: 'Execution status',
  事件目标: 'Event Target',
  事件级别: 'Event Severity',
  事件状态: 'Event Status',
  告警关注人: 'Alarm Follower',
  告警汇总ID: 'Alarm List ID',
  告警详情: 'Alarm Details',
  告警组详情: 'Alarm Team Details',
  告警中: 'Alarming',
  告警组: 'Alarm Team',
  告警源: 'Alarm Source',
  告警数: 'Alarms',
  告警数量: 'Alarms',
  告警屏蔽详情: 'Alarm Mute Details',
  告警风暴: 'Alarm Storm',
  告警阶段: 'Alarm Stage',
  告警标题: 'Alarm Title',
  告警通知模板: 'Message Template',
  告警组名称: 'Alarm Team Name',
  告警等级: 'Alarm Severity',
  触发告警数: 'Triggered alarms',
  防御告警数: 'Defense alarms',
  告警目标: 'Alarm Target',
  告警配置: 'Alarm Config',
  有告警: 'Alarming', // 查看语境
  所有告警: 'All alarms',
  告警事件: 'Alarm event',
  致命告警: 'Emergency alarm',
  预警告警: 'Critical alarm',
  提醒告警: 'Warning alarm',
  异常告警: 'Abnormal alarm',
  告警空空: 'Alarm empty',
  告警列表: 'Alarms',
  告警分派: 'Alarm Routing',
  告警产生: 'Alarm Generated',
  告警收敛: 'Alarm Convergence',
  告警恢复: 'Alarm Recovery',
  告警关闭: 'Alarm Close',
  告警触发: 'Alarm Triggered',
  告警次数: 'Alarms',
  告警状态: 'Alarm Status',
  告警处理: 'Alarm Handling',
  告警级别: 'Alarm Severity',
  较多告警: 'More alarms',
  较少告警: 'Less alarms',
  历史告警: 'Alarm History',
  通知升级: 'Upgrade',
  调整等级: 'Adjustment Level ',
  追加标签: 'Append Tags',
  分派动作: 'Dispatch Action',
  新建规则组: 'New Group',
  // 策略
  策略名: 'Rule Name',
  策略ID: 'Rule ID',
  策略类型: 'Rule Type',
  APM策略说明: 'APM Rule Reference',
  K8s策略说明: 'K8s Rule Reference',
  策略详情: 'Rule Details',
  屏蔽策略: 'Mute alert rule',
  策略屏蔽: 'Mute by Rule ',
  策略内容: 'Rule Content',
  应用策略数: 'Related Rules',
  策略: 'Alert Rule',
  策略项: 'Rule Item',
  触发策略: 'Trigger Rule',
  监控策略: 'Alert Rule',
  策略导出: 'Rule Export',
  新建策略: 'New Rule',
  相关策略: 'Related Rules',
  策略级别: 'Rule Severity',
  优先级: 'Priority',
  修改组名: 'Modify group name',
  修改优先级: 'Modify priority',
  基于分派规则通知: 'Alarm Routing',
  修改告警内容: 'Modify Alarm',

  // 专业表单 - 图表相关
  已选图表: 'Selected charts',
  视图标签名: 'Label Name',
  编辑视图: 'Edit View',
  视图截取: 'View Capture',
  视图排序: 'View Sort',
  检查视图: 'Visualize',
  场景视图: 'Scene View',
  视图信息: 'View Information',
  视图设置: 'View Settings',
  视图标签: 'View Tag',
  视图名称: 'View Name',
  卡片视图: 'Card Mode',
  列表视图: 'List Mode',
  监控图表: 'Chart',
  图表数量: 'Charts',
  面积图: 'Area Chart',
  线性图: 'Line Chart',
  查看大图: 'Enlarge',
  图表预览: 'Chart Preview',

  // 日志
  日志详情: 'Log Details',
  上报日志详情: 'Reported Log Details',
  日志路径: 'Log Path',
  日志字符集: 'Character Set',
  日志关键字: 'Log Keywords',
  日志: 'Log',
  源日志: 'Source Log',
  操作日志: 'Record',
  日志归档: 'Log Archiving',

  // 关联
  关联告警: 'Composite',
  关联策略: 'Related Rules',
  关联日志: 'Related Logs',
  已关联: 'Related',
  未关联: 'No Related',
  关联日历: 'Associated Calendar',
  关联应用: 'Related App',
  被关联: 'Associated by',
  关联查看: 'Related Views',

  // 匹配
  匹配类型: 'Match Type',
  精确匹配: 'Exact Match',
  字段名匹配: 'Field Match',
  字段名模糊匹配: 'Fuzzy Match',
  通配符匹配: 'Wildcard Match',
  正则匹配: 'Regular Match',
  范围匹配: 'Range Match',
  匹配内容: 'Matching Content',
  自动匹配: 'Auto Match',
  进程匹配: 'Process Match',
  匹配方式: 'Match Match',
  域名匹配: 'Domain Match',
  '通配符匹配:': 'Wildcard Matching:',
  '字段名匹配(*代表通配符):': 'Field name matching (* stands for wildcard): ',
  '字段名匹配(代表通配符):': 'Field name matching ( represents wildcard):',
  '字段名模糊匹配:': 'Fuzzy Field Name Matching:',
  '关键字规则:': 'Keyword rule: ',
  关键字规则: 'Keyword Rules',

  // 耗时
  总耗时: 'Duration',
  耗时: 'Duration',
  阶段耗时: 'Phase time',
  执行耗时: 'Execution time',
  '阶段耗时 (同步)': 'Phase time (sync)',

  // 名
  字段名: 'Field Name',
  变量名: 'Variable Name',
  数据源名: 'Datasource Name',
  插件名: 'Plugin Name',
  分类名: 'Category Name',
  表名: 'Table Name',
  数据名: 'Data Name',
  插件别名: 'Plugin Alias',
  服务名: 'Service Name',
  域名: 'Domain Name',
  搜索收藏名: 'Search Favorites',
  别名: 'Alias',
  组名: 'Group Name',
  收藏名: 'Favorite Name',
  事件名: 'Event Name',
  主机名: 'Host Name',
  集群名: 'Set Name',
  模块名: 'Module Name',
  重命名: 'Rename',
  英文名: 'English Name',
  表别名: 'Table Alias',
  用户名: 'Username',
  团体名: 'Group Name',
  安全名: 'Safety Name',
  进程名: 'Process Name',
  显示名: 'Display Name',
  规则组名: 'Rule group name',

  // ID
  分类ID: 'Category ID',
  服务实例ID: 'Instance ID',
  平台事件ID: 'Platform Event ID',
  数据ID: 'Data ID',
  事件ID: 'Event ID',
  特性ID: 'Feature ID',
  插件ID: 'Plugin ID',
  套餐ID: 'Solution ID',
  设备ID: 'Device ID',
  主机ID: 'Host ID',
  'ID:': 'ID:',
  '插件ID:': 'Plugin ID:',
  云区域ID: 'BK-Network Area ID',
  索引集ID: 'Indices ID',
  全局事件ID: 'Global Event ID',
  处理记录ID: 'Handling Record ID',
  目标云区域ID: 'Target BK-Network Area ID',
  目标服务实例ID: 'Target Instance ID',
  自定义事件分组ID: 'Custom Event Group ID',

  // 类型
  类型: 'Type',
  轮值类型: 'On-call Type',
  插件类型: 'Plugin Type',
  项目类型: 'Project Type',
  采样类型: 'Sampling Type',
  远程服务类型: 'Remote Service Type',
  事件类型: 'Event Type',
  节点类型: 'Node Type',
  映射类型: 'Mapping Type',
  目标类型: 'Target Type',
  算法类型: 'Algorithm Type',
  失效类型: 'Failure Type',
  查询类型: 'Query Type',
  套餐类型: 'Solution Type',
  调用类型: 'Invocation Type', // 需要看语境
  数据类型: 'Data Type',

  // 参数
  参数别名: 'Alias',
  参数类型: 'Type',
  参数说明: 'Description',
  参数配置: 'Parameter Config',
  参数名称: 'Name',
  填写参数: 'Fill in',
  运行参数: 'Parameters',
  参数: 'Parameter',
  参数填写: 'Parameter filling',
  定义参数: 'Define Parameters',
  位置参数: 'Positional Variables',
  监听参数: 'Bind Parameters',
  参数设置: 'Parameter Settings',

  // 说明
  反馈: 'Feedback',
  说明: 'Description',
  插件变更说明: 'ChangLog',
  上报格式说明: 'Format Help',
  接入指引: 'Guide',
  文档说明: 'Manual',
  插件说明: 'Plugin Manual',
  使用说明: 'Usage',
  套餐说明: 'Solution Manual',
  依赖说明: 'Dependency Notes',
  集群说明: 'Description',
  模板使用说明: 'Template Usage',
  系统事件说明: 'System Event Reference',

  // 详情
  应用详情: 'Application Details',
  详情: 'Details',
  主机详情: 'Host Details',
  节点详情: 'Node Details',
  查看详情: 'View Details',
  处理详情: 'Handling Details',
  执行详情: 'Execution Details',
  套餐详情: 'Solution Details',
  插件详情: 'Plugin Details',
  服务详情: 'Service Details',
  事件详情: 'Event Details',
  变量详情: 'Variables',
  处理状态详情: 'Handling Status Details',
  处理记录详情: 'Handling Record Details',
  相关主机详情: 'Related Host Details',
  操作的执行详情: 'Execution Details',

  // 汇聚
  汇聚方法: 'Agg Method',
  汇聚周期: 'Agg Interval',
  汇聚: 'Agg',
  是否汇聚: 'Aggregation',

  // 任务
  任务名: 'Task Name',
  任务ID: 'Task ID',
  拨测任务ID: 'Check ID',
  任务组: 'Check Group',
  任务组名称: 'Group Name',
  后台任务: 'Background Task',
  拨测任务: 'Check',
  任务名称: 'Check name',
  空任务组: 'Empty Group',
  绑定任务: 'Bind Task',
  任务状态: 'Task Status',
  任务列表: 'Tasks',

  // 业务
  '-我有告警的业务-': '-Alarming Business-',
  业务ID: 'Business ID',
  新业务接入详情: 'Access Details',
  业务拓扑: 'Business Topology',
  业务数: 'Businesses',
  本业务可见: 'This Business',
  本业务: 'This Business',
  业务集群: 'Business Set',
  业务模块: 'Business Module',
  全业务: 'All business',
  新建业务: 'New Business',
  '有权限的业务(最大20个)': 'Authorized (max 20)',
  有权限的业务: 'Authorized Business',

  // 空间
  空间名: 'Space Name',
  空间ID: 'Space ID',
  空间管理: 'Space Management',
  空间数: 'Spaces',
  本空间: 'This Space',
  监控空间: 'Monitoring Space',
  我的空间: 'My Spaces',
  全部空间: 'All Spaces',
  空间列表: 'Spaces',
  空间范围: 'Spaces',
  空间: 'Space',
  项目空间: 'Project Space',

  // 指标
  日志平台指标: 'BKLog',
  关联指标: 'Related Metric',
  指标名: 'Metric Name',
  指标别名: 'Metric Alias',
  指标ID: 'Metric ID',
  分组ID: 'Group ID',
  指标类型: 'Metric Type',
  拨测指标说明: 'Metric Reference',
  APM指标说明: 'Metric Reference',
  容器指标说明: 'Metric Reference',
  指标详情: 'Metric Reference',
  计算平台指标: 'BKBase',
  预览: 'Preview',
  指标数: 'Metrics',
  指标名称: 'Metric Name',
  监控指标: 'Metrics',
  后台服务器性能指标: 'Performance',
  指标项: 'Metric Item',
  指标: 'Metrics',
  指标值: 'Metric Value',
  指标选择: 'Metric Selection',
  指标分类: 'Metrics Classification',
  监控采集指标: 'BKMonitor',
  全部指标: 'All metrics',
  指标列表: 'Metrics',

  // 维度
  维度名: 'Dimension Name',
  维度别名: 'Dimension Alias',
  管控区域ID: 'BK-Network Area ID',
  指标维度: 'Metric / Dimension',
  屏蔽维度: 'Dimension',
  维度屏蔽: 'Mute by Dimension',
  维度: 'Dimension',
  维度选择: 'Dimension',
  维度过滤: 'Dimension',
  维度条件: 'Conditions',
  维度名称: 'Dimension Name',
  维度下钻: 'Facet',
  异常维度: 'Abnormal Dimension',

  // - Title Text：标题文本，用于描述页面或模块的主题，例如“登录页面”、“用户信息”等。

  '通知 & 流程': 'Notification & Process',
  '等级 & 标签': 'Grades & Labels',
  监控对象总览: 'Monitoring Layer',

  // - Subtitle Text：副标题文本，用于进一步描述页面或模块的主题，例如“欢迎登录”、“个人信息”等。

  // - Front-end Components：前端组件类，如时间选择器、目标选择器等。

  // 通用组件 ， 时间选择器
  每个工作日: 'Every working day',
  月: 'Month',
  周: 'Week',
  年: 'Year',
  天: 'Day',
  昨天: 'Yesterday',
  一: 'One',
  二: 'Two',
  三: 'Three',
  四: 'Four',
  五: 'Five',
  六: 'Six',
  '1 天': '1 day',
  '7 天': '7 days',
  '1 周': '1 week',
  '1 月': '1 month',
  '10 秒': '10 s',
  '20 秒': '20 s',
  '30 秒': '30 s',
  '60 秒': '60 s',
  '15 天': '15 days',
  周一: 'Monday',
  周二: 'Tuesday',
  周三: 'Wednesday',
  周四: 'Thursday',
  周五: 'Friday',
  周六: 'Saturday',
  周日: 'Sunday',
  '1 分钟': '1 m',
  '2 分钟': '2 m',
  '5 分钟': '5 m',
  '1 小时': '1 hour',
  '1 个月': '1 month',
  '10 分钟': '10 m',
  '30 分钟': '30 minutes',
  '12 小时': '12 hours',
  一个月: '1 month',
  一月前: 'Previous month',
  星期一: 'Monday',
  星期二: 'Tuesday',
  星期三: 'Wednesday',
  星期四: 'Thursday',
  星期五: 'Friday',
  星期六: 'Saturday',
  星期日: 'Sunday',
  今天: 'Today',
  前天: 'Day before yesterday',
  本周: 'This week',
  '{n} 天前': '{n} days ago',
  '{n} 周前': '{n} weeks ago',
  '{n} 月前': '{n} months ago',
  每天重复: 'Every day',
  每周重复: 'Every week',
  每月重复: 'Every month',
  每年重复: 'Every year',
  每分钟: 'Every minute',
  每五分钟: 'Every 5 minutes',
  每天: 'Daily',
  每周: 'Weekly',
  每月: 'Monthly',
  频率: 'Frequency',
  全天: 'All day',
  小时: 'Hour',
  按天: 'Daily',
  按周: 'Weekly',
  按月: 'Monthly',
  上周: 'Last week',
  分: 'min', // 如果这个只用于指标检索的周期单位没有问题， 如果还有其他地方需要确认

  '1 小时前': '1 hour ago',
  '7 日内': 'Last 7 days',
  一月内: 'Last 1 month',
  '近 1 天': 'Last 1 day',
  '近 3 天': 'Last 3 days',
  '近 7 天': 'Last 7 days',
  '近 15 天': 'Last 15 days',
  '近 30 天': 'Last 30 days',
  '近 1 小时': 'Last 1 hour',
  '近 {n} 天': 'Last {n} days',
  '近{n}分钟': 'Last {n} minutes',
  '近{n}小时': 'Last {n} hours',
  '近 12 小时': 'Last 12 hours',

  // ip选择器
  拓扑节点: 'Topology Node',
  动态拓扑: 'Dynamic Topology',

  // 通用功能-其他

  是: 'Yes',
  否: 'No',
  值: 'value',
  个: '', // 看语境，这种不需要单独有
  空: 'Empty',
  日: 'Day',
  次: 'Times',
  升: 'Rise',
  降: 'Fall',
  秒: 's',
  如: 'Like',
  关: 'Close',
  开: 'Open',
  条: 'Item',
  且: 'and',
  或: 'or',

  // 枚举列表-状态、候选值 ，标签状态

  未启用: 'Not Enabled',
  启动中: 'Starting',
  停止中: 'Stopping',
  运行中: 'Running',
  启用中: 'Enabling',
  停用中: 'Disabling',
  加载中: 'Loading',
  '初始化中...': 'Init...',
  回中: 'Return',
  测试中: 'Testing',
  导入中: 'Importing',
  执行中: 'Running',
  部署中: 'Deploying',
  准备中: 'Preparing',
  等待中: 'Waiting',
  调试中: 'Debugging',
  已停用: 'Disabled',
  已删除: 'Deleted',
  已上线: 'Online',
  已恢复: 'Recovered',
  已关闭: 'Closed',
  已收敛: 'Converged',
  已勾选: 'Selected',
  已取消: 'Cancelled',
  已发送: 'Sent',
  已加载: 'Loaded',
  已选择: 'Selected',
  已失效: 'Expired',
  已启用: 'Enabled',
  已过期: 'Expired',
  已认证: 'Authenticated',
  已反馈: 'Feedback',
  已命中: 'Hit',
  已安装: 'Installed',
  已下架: 'Unavailable',
  已处理: 'Handled',
  已确认: 'Confirmed',
  已回收: 'Recycled',
  已收藏: 'Favorited',
  已经添加: 'Added',
  已选条件: 'Selected conditions',
  已选主机: 'Selected hosts',
  已有项目: 'Existing projects',
  只看已选: 'Show only selected',

  // 操作
  '添加/编辑IP': 'Add/Edit IP',
  '添加/编辑域名': 'Add/Edit Domain',
  '添加/编辑URL': 'Add/Edit URL',

  // 标题
  编辑: 'Edit',
  编辑页签: 'Edit Tab',
  编辑变量: 'Edit Variable',
  编辑事项: 'Edit Item',
  编辑插件: 'Edit Plugin',
  编辑收藏: 'Edit Favorite',

  // 专业术语
  常用的: 'Starred',
  致命: 'Emergency',
  预警: 'Critical',
  提醒: 'Warning',
  发送历史: 'Send History',
  全站搜索: 'Site-wide search',
  用户体验: 'User Experience',
  平台设置: 'Platform Settings',
  插件制作: 'Plugin production',
  观测场景: 'Observations',
  处理记录: 'Handling Records',
  全局设置: 'Global Settings',
  迁移工具: 'Migration Tool',
  日历服务: 'Calendar Service',
  监控对象: 'Monitoring Layer',
  综合拨测: 'Synthetic Monitoring',

  // 通用表单 - 数

  总数: 'Total',
  执行数: 'Executions',
  节点数: 'Nodes',
  索引数: 'Indices',
  事件数: 'Events',
  主机数量: 'Hosts',
  总执行数: 'Executions',
  文档数量: 'Documents',

  // 专业名词  屏蔽

  屏蔽时间: 'Mute Time',
  屏蔽ID: 'ID',
  屏蔽中: 'Muted',
  已屏蔽: 'Muted',
  屏蔽周期: 'Mute Period',
  屏蔽范围: 'Mute Range',
  屏蔽: 'Mute',
  屏蔽级别: 'Mute Severity',
  屏蔽状态: 'Mute Status',
  解除屏蔽: 'Unmute',
  快捷屏蔽: 'Mute',
  屏蔽失效: 'Unmute',
  范围屏蔽: 'Mute by Range ',
  新建屏蔽: 'New Mute',

  // 专业名称 IP
  IP类型: 'IP Type',
  IP目标: 'IP Target',
  内网IP: 'Intranet IP',
  外网IP: 'Public IP',
  目标IP: 'Target IP',
  主机IP: 'Host IP',
  其他IP: 'Other IP',
  监听IP: 'Bind IP',
  目标IPv6: 'Target IPv6',

  // 专业表单 邮件订阅

  订阅: 'Subscribe',
  订阅内容: 'Content',
  订阅状态: 'Status',
  取消订阅: 'Unsubscribe',
  重新订阅: 'Re-subscribe',
  邮件标题: 'Mail Title',
  管理员: 'Administrator',
  发送频率: 'Send Frequency',
  数据范围: 'Data Range',
  子标题: 'Subtitle',
  模块布局: 'Layout',
  订阅时间: 'Time',
  已订阅: 'Subscribed',
  删除订阅: 'Delete Subscription',
  邮件订阅: 'Email Subscription',
  包含周末: 'Weekends',
  整屏截取: 'Full-Screen Capture',
  完整查看: 'Complete View',
  按发送频率: 'By Sending Frequency',
  每个小时整点发送: 'Send every hour on the hour',
  每个工作日重复: 'Repeat Every Workday',

  当前版本: 'Latest',
  所属: 'Spaces',
  是否启用: 'Enabled',
  触发条件: 'Trigger Condition',
  恢复条件: 'Recovery Condition',
  通知间隔: 'Notification Interval',
  降噪设置: 'Noise Reduction',
  模型名称: 'Model Name',
  对象包含: 'Object Contains',
  防御规则: 'Defense Rule',
  监控条件: 'Condition',
  计算公式: 'Formula',
  关键字: 'Keyword',
  周期: 'Interval',
  函数: 'Function',
  通知对象: 'Recipients',
  分派原因: 'Reason',
  查询语句: 'Query Statement',
  通知方式: 'Method',
  执行通知: 'Execution Notification',
  事件名称: 'Event Name',
  数据名称: 'Data Name',
  应用名称: 'App Name',
  数据源: 'Datasources',
  数据源类别: 'Datasource Type',
  数据源格式: 'Datasource Format',
  操作系统: 'OS',
  Agent状态: 'Agent Status',
  单位: 'Unit',
  开启轮值: 'On-call',
  轮值设置: 'On-call Settings',
  生效时段: 'Effective Period',
  绑定端口: 'Bind Port',
  绑定主机: 'Bind Post ',
  SNMP版本: 'SNMP Version',
  默认值: 'Default',
  数据抓取周期: 'Fetch Interval',
  调试进度: 'Debugging Drogress',
  对象: 'Object',
  排除规则: 'Exclusion Rules',
  端口探测: 'Port Detection',
  上报协议: 'Report Protocol',
  是否为平台事件: 'Is platform event',
  作用范围: 'Scope',
  名称: 'Name',
  注意事项: 'Notes',
  使用方法: 'Usage',
  命令行直接调用样例: 'Command line',
  数据上报端点样例: 'Endpoint',
  sdk接入流程: 'SDK Docs',
  各语言接入示例: 'Examples',
  数据上报格式样例: 'Format Example',
  可选项: 'Optional',
  所属组织: 'Organization',
  检测算法: 'Detection Algorithm',
  回调地址: 'Callback Address',
  推送通知: 'Push notification',
  协议: 'Protocol',
  目标地址: 'Target address ',
  超时设置: 'Timeout Settings',
  期待返回码: 'Expected return code',
  期待响应信息: 'Expected response information',
  地理位置: 'Geographic',
  节点名称: 'Probe Name',
  地区: 'Region',
  运营商: 'Carrier',
  消息通知渠道: 'Channel',

  // 自监控
  监控后台服务状态: 'Service Status',
  数据链路: 'Data Link',
  后台服务: 'Background Service',
  监控Saas依赖周边组件状态: 'Component Status',
  归属日历: 'Attribution Calendar',
  页签名称: 'Tab Name',
  是否展示数字: 'Display Numbers',
  绑定关系: 'Binding',
  仪表盘名称: 'Dashboard Name',
  所属目录: 'Category',
  映射范围: 'Mapping Range',
  通知状态: 'Notification Status',
  支持插件: 'Support Plugin',
  支持语言: 'Support Language',
  支持环境: 'Support Environment',
  查询方式: 'Query Method',
  收藏描述: 'Description',
  远程调用: 'Remote Call',
  网页: 'Web',

  // APM

  英文名称: 'English Name',
  基础信息: 'Basic Information',
  Apdex设置: 'Apdex Setting',
  采样比例: 'Sampling Ratio',
  样例展示: 'Sample Display',
  存储集群: 'Storage Cluster',
  物理索引: 'Physical Index',
  主分片: 'Primary Shard',
  副本分片: 'Replica Shard',
  URI源: 'URI Source',

  IMAP端口: 'IMAP Port',
  实例数: 'Instances',
  主机数: 'Hosts',
  实例名称: 'Instance Name',
  处理时长: 'Duration',
  具体内容: 'Detailed Content',
  处理状态: 'Handling Status',
  排除主机: 'Excluded Hosts',
  源映字段规则: 'Mapping Field',
  字段: 'Field',
  自定义事件: 'Custom Event',
  布局: 'Layout',
  触发信号: 'Trigger Signal',
  监控数据: 'Monitoring Data',

  // 收藏

  所属组: 'Group',
  可见范围: 'Visible',
  新检索: 'New Explore',
  收藏查询: 'Favorite',
  '(仅个人可见)': '(personal)',
  未分组: 'Default',
  收藏排序: 'Favorite Sorting',
  '按名称 A - Z 排序': 'Sort by name A - Z',
  '按名称 Z - A 排序': 'Sort by name Z - A',
  按更新时间排序: 'Sort by time',
  共享: 'Share',

  // 分享
  页面路径: 'Page Path',
  本次分享URL: 'This URL',

  纬度值: 'Dimension value',
  插件选择: 'Plugin selection',
  开启应用监控: 'Turn on APM',
  快捷链接: 'Shortcut Link',
  敏感度: 'Sensitivity',
  预测值: 'Predicted value',
  手游: 'Mobile game',
  端游: 'Client game',
  页游: 'Web game',
  其他: 'Other',
  今日: 'Today',
  拷贝: 'Copy',
  隐藏: 'Hide',
  展示: 'Display',
  源码: 'Source code',
  条件: 'Condition',
  索引: 'Index',
  索引集名称: 'Indices Name',

  目标: 'Target',
  不限: 'Unlimited',
  数字: 'Number',
  文本: 'Text',
  省份: 'Province',
  版本: 'Version',
  正常: 'Normal',
  异常: 'Exception',
  升级: 'Upgrade',
  克隆: 'Clone',
  列表: 'List',
  降序: 'Descending',
  未知: 'Unknown',
  进程: 'Process',
  离线: 'Offline',

  // 检索
  自动: 'Auto',
  原始: 'Raw',
  检索: 'Explore',
  检索语句: 'Search statement',
  数据查询: 'Data Query',

  注意: 'Note',
  目录: 'Directory',
  关注: 'Follow',
  有效: 'Effective',
  导航: 'Navigation',
  插件: 'Plugin',
  描述: 'Description',
  持续: 'Continuous',
  主机: 'Host',
  有用: 'Useful',
  分屏: 'Split-Screen',
  复制: 'Copy',
  认证: 'Authentication',
  主体: 'Subject',
  默认: 'Default',
  包含: 'Include',
  正则: 'Regular expression',
  放弃: 'Abandon',
  端口: 'Port',
  空格: 'Space',
  内置: 'Built-in',
  升序: 'Ascending',
  缩小: 'Shrink',
  全屏: 'Full screen',
  总览: 'Overview',
  退出: 'Exit',
  连续: 'Consecutive',
  较前: 'Compared to previous',
  上升: 'Rise',
  下降: 'Declining',
  过去: 'Past',
  移动: 'Move',
  联通: 'Unify',
  电信: 'Telecom',
  进行: 'Processing',
  筛选: 'Filter',
  全选: 'Select All',
  累计: 'Accumulate',
  事件: 'Events',
  实时: 'Stream',
  级别: 'Severity',
  启用: 'Enable',
  停用: 'Disable',
  分钟: 'Minute',
  确定: 'Confirm',
  谨慎: 'Caution',
  高危: 'High risk',
  普通: 'Normal',
  固定: 'Fixed',
  递增: 'Incremental',
  支持: 'Support',
  刷新: 'Refresh',
  关闭: 'Close',
  向上: 'Upward',
  向下: 'Downward',
  静态: 'Static',
  动态: 'Dynamic',
  下钻: 'Drill down',
  单次: 'Single',
  官方: 'Official',
  可用: 'Available',
  草稿: 'Draft',
  英文: 'English',
  密码: 'Password',
  私钥: 'Private Key',
  文件: 'File',
  开关: 'Switch',
  分组: 'Group',
  系统: 'System',
  如下: 'As follows',
  方式: 'Method',
  排除: 'Exclude',
  服务: 'Service',
  重试: 'Retry',
  终止: 'Terminate',
  变更: 'Change',
  创建: 'Create',
  公共: 'Public',
  堆栈: 'Stack',
  数据: 'Data',
  手动: 'Manual',
  选取: 'Select',
  继续: 'Continue',
  起始: 'Start',
  体验: 'Experience',
  处理: 'Handling',
  文档: 'Documentation',
  我的: 'Mine',
  场景: 'Scene',
  设置: 'Setting',
  总共: 'Total',
  准备: 'Prepare',
  接口: 'Interface',
  数量: 'Quantity',
  表格统计: 'Table',
  通知: 'Notification',
  默认通知: 'Default',
  轻微: 'Minor',
  热度: 'Popularity',
  来源: 'Source',
  总量: 'Total',
  分词: 'Segmentation',
  拓扑: 'Topology',
  随机: 'Random',
  相等: 'Equal',
  健康: 'Healthy',
  拉取: 'Pull',
  推送: 'Push',
  执行: 'Execute',
  概述: 'Overview',
  监控: 'Monitoring',
  开启: 'Enable',
  应用: 'Application',
  公开: 'Public',
  私有: 'Private',
  副本: 'Copy',
  检查: 'Check',
  情况: 'Condition',
  回收: 'Recycle',
  要求: 'Requirements',
  降噪比: 'NRR',
  数据库: 'Database',
  字符串: 'String',
  仪表盘: 'Dashboard',
  未保存: 'Unsaved',
  管控区域: 'BK-Network Area',
  主机列表: 'Hosts',
  不刷新: 'No Refresh',
  自定义: 'Custom',
  新开页: 'New Tab',
  不重复: 'No Repeat',
  未恢复: 'Active',
  被收敛: 'Converged',
  可选择: 'Selectable',
  不包含: 'Exclude',
  头信息: 'Header',
  仅一次: 'Once only',
  用户组: 'User Group',
  不支持: 'Not Supported',
  当前值: 'Value',
  监控项: 'Item',
  创建方式: 'Create Method',
  索引集: 'Indices',
  结果表: 'Result Table',
  未安装: 'Not installed',
  根节点: 'Root Node',
  被解除: 'Resolved',
  强密码: 'Strong password',
  待确认: 'To be confirmed',
  必选项: 'Required',
  选项有: 'Options',
  非认证: 'Non-Authenticated',
  全平台: 'All platforms',
  预览值: 'Preview',
  状态码: 'Status Code',
  将下架: 'Discontinued',
  空闲率: 'Idle Rate',
  不相等: 'Not equal',
  丢弃率: 'Drop rate',
  '推荐接⼊': 'Recommended',
  最近访问: 'Recently',
  研发项目: 'BK-CI',
  容器项目: 'BCS',
  平台产品: 'Platform products',
  支撑产品: 'Supported products',

  // 拨测
  自建拨测: 'Self-built Probe',
  获取权限: 'Get permission',
  常用导航: 'Starred',
  数据示例: 'Data Example',
  数据总览: 'Data Overview',
  展示设置: 'Display Settings',
  拨测监控: 'Synthetics',
  服务监控: 'Services',
  进程监控: 'Processes',
  主机监控: 'Hosts',
  快速设置: 'Quick Settings',
  查看更多: 'View more',
  拨测节点: 'Probes',
  拨测列表: 'Checks',
  消息队列: 'Message queue',
  服务拨测: 'Service probe',
  关联任务数: 'Related',
  '自建节点(公共)': 'Self-built node (public)',
  '自建节点(私有)': 'Self-built node (private)',
  '节点名称/IP': 'Node Name/IP',
  '任务名称（必填）': 'Task name (required)',
  HTTP回调: 'HTTP callback',
  HTTP服务: 'HTTP service',
  IMAP地址: 'IMAP address',
  nathan拨测测试节点平均值可用率: 'Average availability rate of nathan probe test nodes', // 查看中文位置 有错误的字符
  nathan拨测测试节点平均值响应时间: 'Average response time of Nathan probe test node', // 查看中文位置 有错误的字符
  SSL证书校验: 'SSL Certificate Verification',
  URI匹配: 'URI match',
  使用SSL: 'SSL Enabled',
  采集URL: 'Collection URL',
  探测包大小: 'Probe Packet Size',
  可用率图例: 'Legend',
  响应时长图例: 'Response time legend',
  当前可用率仅: 'Current availability rate only', // 查看位置 不完整
  当前服务可用率: 'Current Service Availability',
  拨测站点可用率趋势对比: 'Comparison of probe site availability trends',
  蓝鲸官网节点平均可用率: 'Average availability rate of BlueKing official website nodes', // 查看位置 不完整
  最快响应时长: 'Fastest Response Time',
  最慢响应时长: 'Slowest Response Time',
  平均响应时长: 'Average Response Time',
  期待响应格式: 'Expected response format',
  发送字节流量: 'Sent Byte Traffic',

  数据步长: 'Data Step',
  点击全选: 'Select All',
  内容: 'Content',
  查看语法: 'View Syntax',
  过滤条件: 'Filtering conditions',
  监控目标: 'Scope',
  系统事件: 'System Event',
  应用监控: 'Application Monitoring',
  响应时长: 'Response Time',
  所属分组: 'Group',
  显示全部: 'Show All',
  点击更换: 'Change',
  点击上传: 'Upload',
  模板下载: 'Template Download',
  OS名称: 'OS Name',
  集群模块: 'Set / Module',
  可选条件: 'Optional',
  主机筛选: 'Host Filtering',
  还原默认: 'Restore Default',
  本页全选: 'This page',
  跨页全选: 'All Pages',
  系统进程: 'System Process',
  运行时长: 'Duration',
  筛选条件: 'Filtering Conditions',
  索引来源: 'Index Source',
  完整格式: 'Full Format',
  快捷格式: 'Shortcut Format',
  取消收藏: 'Unfavorite',
  对比方式: 'Compare',
  我的收藏: 'My Favorites',
  服务状态: 'Service Status',
  解决方案: 'Solution',
  通知设置: 'Notification Settings',
  接口列表: 'Endpoints',
  可能原因: 'Possible Reasons',
  规则名称: 'Rule Name',
  详细信息: 'Details',
  事项列表: 'Items',

  // 日历相关
  日历: 'Calendar',
  日历列表: 'Calendars',
  内置日历: 'Built-in Calendar',
  新建日历: 'New Calendar',
  全部删除: 'Delete All',
  结束日期: 'End Date',
  全部修改: 'Modify All',
  新建事项: 'New Item',
  永不结束: 'Never Ends',
  具备功能: 'Functions',
  取消置顶: 'Unpin',
  内容范围: 'Content Range',
  接口测试: 'Interface Testing',
  重新测试: 'Retest',

  // 告警事件
  记录分析: 'Record Analysis',
  高级筛选: 'Advanced',
  事件中心: 'Event Center',
  执行对象: 'Execution object',
  手动处理: 'Manual handling',
  再次处理: 'Hand again',
  一键拉群: 'WeCom',
  群聊邀请: 'Invite WeCom',
  处理经验: 'Experience',
  流转记录: 'Records',
  延迟恢复: 'Delayed recovery',
  中断恢复: 'Interruption recovery',
  处理动作: 'Handling Actions',
  处理次数: 'Handling',
  处理明细: 'Handling Details',
  查看全部: 'View All',
  完整列表: 'Full',
  页签设置: 'Tab Settings',
  变量设置: 'Variable Settings',
  切换视角: 'Switch View',
  精准过滤: 'Strict Filter',
  提交内容: 'Submit Content',
  请求内容: 'Request Content',
  请求超时: 'Request Timeout',
  重试间隔: 'Retry Interval',
  重试次数: 'Retry Times',
  回调间隔: 'Callback Interval',
  返回列表: 'Return',
  等待响应: 'Waiting for response',
  自建节点: 'Self-built Node',
  新建项目: 'New project',
  目录名称: 'Directory Name',
  格式文件: 'Format file',
  'AI 配置': 'AI CONFIG',
  文档链接: 'Documentation Link',
  现有主机: 'Existing Hosts',
  包发送量: 'Package Sent',
  包接收量: 'Package Reception',
  服务地址: 'Service Address',
  默认分类: 'Default Classification',
  是保留字: 'Reserved Word',
  全局标签: 'Global tag',
  部分异常: 'Exceptions',
  开始升级: 'Start Upgrade',
  准备升级: 'Preparing for upgrade',
  处理建议: 'Handling suggestions',
  持续时长: 'Duration',
  通知内容: 'Notification content',
  项目管理: 'Project Management',
  所属模块: 'Module',
  下载样例: 'Download Sample',
  一级分类: 'Top Category',
  二级分类: 'Sub Category',
  前一时刻: 'Previous Moment',
  故障自愈: 'Fault self-healing',
  全部收起: 'Collapse All',
  全部展开: 'Expand All',
  拨测地址: 'Probe Address',
  请求方法: 'Request Method',
  基本信息: 'Basic Information',
  判断条件: 'Judgment Condition',
  核心内容: 'core content', // 查看语义
  变量列表: 'Variables',
  模板预览: 'Template Preview',
  检测规则: 'Detection Rules',
  服务分类: 'Service Classification',
  通知场景: 'Notification Scenarios',
  默认全部: 'Default is All',
  不能混用: 'Cannot be Mixed',
  静态拓扑: 'Static Topology',
  服务模板: 'Service Template',
  集群模板: 'Cluster Template',
  移除所有: 'Remove All',
  当前轮值: 'Current rotation',
  执行阶段: 'Execution phase',
  变更记录: 'Change log',
  搜索函数: 'Search function',
  结果预览: 'Result Preview',
  全部选项: 'All options',
  事件数量: 'Events',
  方案描述: 'Solution Description',
  日期范围: 'Date Range',
  当前成员: 'Current Members',
  是否交接: 'Handover',
  轮值预览: 'Rotation Preview',
  '当前事件流水过多，收敛{count}条。': 'There are too many current event flows, converge {count} of them.',
  '当前值 - 前一时刻值{0}过去{1}天内任意一天同时刻差值 x{2}+{3}':
    'Current value - previous time value {0} difference x{2}+{3} at any time in the past {1} days',
  '当前值 {0} 过去{1}天内同时刻绝对值 ×{2}+{3}':
    'Current value {0} Absolute value at the same time in the past {1} days × {2} + {3}',
  '当前值与前一时刻值 >={0}且，之间差值 >= 前一时刻 ×{1}+{2}':
    'The difference between the current value and the previous moment value >={0} and the difference between >= previous moment ×{1}+{2}',
  '当前值较上周同一时刻{0}时触发告警':
    'Trigger an alarm when the current value is higher than at the same time last week ({0}).',
  '当前值较前一时刻{0}时触发告警': 'Alarm triggered when the current value is {0} times earlier than the previous time',

  // 处理套餐

  套餐名: 'Solution Name',
  编辑套餐: 'Edit Situation',
  套餐内容: 'Solution Content',
  执行套餐: 'Execution Solution',
  处理套餐: 'Alarm Solution',
  套餐名称: 'Solution Name',
  套餐信息: 'Solution Information',
  新建套餐: 'New Solution',

  // 插件
  插件名称: 'Plugin Name',
  定义插件: 'Define Plugin',
  插件调试: 'Plugin Debugging',
  监听端口: 'Bind Port',
  绑定地址: 'Bind Address',
  设备端口: 'Device Port',
  认证信息: 'Authentication',
  安全级别: 'Security Level',
  验证协议: 'Validate Protocol',
  验证口令: 'Verify Password',
  隐私协议: 'Privacy agreement',
  版本支持: 'Version Support',
  环境依赖: 'Environment',
  主机字段: 'Host Field',
  环境变量: 'Environment Variables',
  默认分组: 'Default Group',
  新建插件: 'New Plugin',
  微软雅黑: 'Microsoft YaHei',
  运行状态: 'Run Status',
  服务实例维度注入: 'Service Instance Dimension Injection',
  主机维度注入: 'Host dimension injection',
  维度注入: 'Dimension Injection',
  命令行匹配: 'Command Line Matching',
  命令行参数: 'Command-line Parameters',
  被关联插件: 'Associated Plugin',
  进程采集插件: 'Process collection plugin',
  '插件名称(ID或别名)': 'Plugin Name (ID or Alias)',
  环境变量参数: 'Environment Variable Parameters',
  非官方插件: 'Unofficial Plugin',
  结合插件提供本地和远程采集两种方式: 'Combined with the plug-in to provide local and remote collection two ways', // 确定位置
  '设置指标&维度': 'Set metrics & dimensions',
  服务实例标签: 'Service instance tags',

  // 采集
  采集时间: 'Collection Time',
  日志采集: 'Log Collection',
  采集项ID: 'Item ID',
  采集ID: 'Collection ID',
  采集详情: 'Collection Details',
  采集接入: 'Collection Access',
  数据采集: 'Data Collection',
  远程采集: 'Remote Collection',
  采集方式: 'Collection Method',
  采集周期: 'Collection Period',
  采集对象: 'Collection Object',
  采集超时: 'Collection Timeout',
  采集项名称: 'Item Name',
  采集: 'Collection',
  新建采集: 'New Collection',
  采集状态: 'Collection Status',
  插件采集: 'Plugin Collection',
  监控采集: 'Monitoring Collection',
  脚本采集: 'Script Collection',
  采集原理: 'Principle',
  采集目标: 'Collection Target',
  采集下发: 'Deploy',
  升级插件: 'Upgrade Plugin',
  回滚操作: 'Rollback',
  确认回滚: 'Confirm Rollback',
  功能介绍: 'Introduction',

  // 配置
  策略配置: 'Rule Configuration',
  视图配置: 'View Configuration',
  关联配置: 'Related Configuration',
  配置说明: 'Instructions',
  配置格式说明: 'Format Help',
  已配置: 'Configured',
  配置管理: 'Management',
  应用配置: 'Application Configuration',
  服务配置: 'Service Configuration',
  配置名称: 'Configuration Name',
  映射配置: 'Mapping Configuration',
  采样配置: 'Sampling Configuration',
  配置: 'Configure',
  未配置: 'Not configured',
  配置指引: 'Guide',
  导入配置: 'Import Configuration',
  配置导出: 'Export Configuration',
  高级配置: 'Advanced',
  配置来源: 'Source',
  配置分组: 'Group',
  配置升级: 'Upgrade',
  采集配置: 'Collection',
  新建配置: 'New Configuration',
  导出配置: 'Export Configuration',
  基础配置: 'Basic Configuration',

  推荐变量: 'Recommended variables',
  变量格式: 'Variable Format',
  服务实例: 'Service instance',
  自动执行: 'Auto Run',
  事件列表: 'Events',
  时序列表: 'Time series',
  目标数量: 'Targets',
  加入分组: 'Join group',
  分组列表: 'Groups',
  数据对象: 'Data Object',
  状态未知: 'Status unknown',
  导入历史: 'Import History',
  时序数据: 'Time series data',
  历史搜索: 'Search History',
  返回概览: 'Return',
  默认方案: 'Default solution',
  关闭对象: 'Close object',
  迁移指引: 'Migration guide',
  明确排除: 'Explicit exclude',
  返回应用: 'Return',
  上钻服务: 'Drill-up',
  数据频率: 'Data frequency',
  容器监控: 'Container monitoring',
  数据应用: 'Data application',
  托管集群: 'Managed cluster',
  场景介绍: 'Scenario introduction',
  你也可以: 'You can also',
  查看文档: 'Refer to documentation',
  主机单位: 'Host unit',
  主机运营: 'Host operation',
  快速接入: 'Quick access',
  迁移状态: 'Migration status',
  查看目标: 'View target',
  高级设置: 'Advanced',
  点击选择: 'Select',
  应用设置: 'Application Settings',
  范围查询: 'Range Query',
  精准查询: 'Exact Query',
  入口接口: 'Entry Endpoint',
  入口服务: 'Entry Service',
  错误分析: 'Error analysis',
  搜索为空: 'Search is empty',
  瀑布列表: 'Waterfall',
  节点拓扑: 'Component',
  异常分值: 'Abnormal Score',
  通用插件: 'General Plugin',
  执行内容: 'Execution content',
  安装插件: 'Install Plugin',
  周边服务: 'Peripheral Services',
  事件插件: 'Event Plugin',
  帮助文档: 'Guide',
  存储状态: 'Storage Status',
  应用列表: 'Applications',
  物理环境: 'Physical Environment',
  容器环境: 'Container Environment',
  插件描述: 'Plugin Description',
  热冷数据: 'Hot/Cold data',
  共享集群: 'Shared cluster',
  安装指引: 'Installation guide',
  存储大小: 'Storage size',
  字段信息: 'Field information',
  数据为空: 'Data is empty',
  操作记录: 'Operation record',
  服务名称: 'Service name',
  说明文案: 'Description',
  监控工具: 'Monitoring Tool',
  拉取频率: 'Pull frequency',
  原始事件: 'Original Event',
  智能设置: 'Smart Settings',
  蓝鲸应用: 'BlueKing SaaS',
  变量选择: 'Variable selection',
  临时分享: 'Temporary Sharing',
  解散分组: 'Dissolve group',
  JS散度: 'JS divergence',
  推荐接入: 'Recommended',
  节点信息: 'Node Info',
  参考例子: 'Example',
  移至分组: 'Move to Group',
  DNS查询模式: 'DNS Mode',
  基于CMDB添加: 'Based on CMDB',
  外网: 'Extranet',
  条件查询: 'Conditional query',
  开启主机监控: 'Enable Host Monitoring',
  预测上界: 'upper',
  预测下届: 'lower',
  调试结果: 'Debug Results',
  流程: 'Process Service',
  查找规则: 'Find rules',
  保持原样: 'No change',
  无用: 'Useless',
  无限制: 'Unlimited',
  无法处理: 'Unable',
  无数据时: 'No-data',
  同步Span数量: 'SYN Spans',
  异步Span数量: 'ASYN Spans',
  内部Span数量: 'Inter Spans',
  未知Span数量: 'Unknown Spans',
  Span类型: 'Span Type',
  最大时间: 'Max Time',
  最小时间: 'Min Time',
  总时间: 'Total Time',
  Kubernetes监控: 'Kubernetes Monitor',
  是否附带图片: 'Picture attached',
  时序图: 'Sequence',
  火焰图: 'Flame',
  内部调用: 'Internal',
  同步被调: 'Sync called',
  同步主调: 'Sync call',
  异步主调: 'Async called',
  异步被调: 'Async call',
  推断: 'Infer',
  占比: 'Ratio',
  鼠标右键有更多菜单: 'Right-click for more menu',
  所属服务: 'Service',
  接口名称: 'Span name',
  'Span 名称': 'Span name',
  '实例 ID': 'Instance ID',
  'SDK 名称': 'SDK name',
  所属Trace: 'Trace',
  显示: 'Show',
  内部邮件: 'Internal mail',
  外部邮件: 'External Mail',
  邮件列表: 'Mail List',
  企业微信群: 'WXWork group',
  群ID: 'Group ID',
  是否附带链接: 'Link attached',
  是否覆盖: 'Overwrite',
  'Messaging 数量': 'Messagings',
  'HTTP 数量': 'HTTPs',
  'RPC 数量': 'RPCs',
  'Async backend 数量': 'Async backends',
  'Other 数量': 'Others',
  'Span 数量': 'Spans',
  'Span 层数': 'Span layers',
  服务数量: 'Services',
  'DB 数量': 'DBs',
  'SDK 版本': 'SDK Version',
  'ID 精准查询': 'ID Exact Query',
  服务入口Span: 'Service Entry Spans',
  接口类型: 'Span Type',
  来源类型: 'Source Type',
  异步: 'Asynchronous',
  同步: 'Synchronous',
  接口名: 'Span Name',
  所属Service: 'Service',
  Span数量: 'Spans',
  错误数: 'Errors',
  错误率: 'Error Rate',
  平均耗时: 'Avg Latency',
  P90耗时: 'P90 Latency',
  P50耗时: 'P50  Latency',
  覆盖: 'Cover',
  不共享: 'Skip',
  数据别名: 'Data Alias',
  被授权人: 'Authorized person',
  授权人: 'Authorized',
  审批记录: 'Approval Record',
  操作权限: 'Permissions',
  操作实例: 'Instances',
  所属空间: 'Space',
  截止时间: 'End date',
  过期: 'Expire',
  失效: 'Invalid',
  '来源于授权人:': 'Authorized person',
  审批中: 'Approval',
  审批成功: 'Approval successful',
  审批失败: 'Approval failed',
  服务类型: 'Service Type',

  // 指标图表类
  告警趋势: 'Alarm Trend',
  平均可用率: 'Average Availability',
  最大可用率: 'Max Availability',
  最小可用率: 'Min Availability',
  '未恢复告警(实时)': 'Unresolved (real-time)',
  未恢复告警: 'Unresolved alerts',
  告警未恢复: 'Unresolved alerts',
  总趋势: 'Total trend',
  执行趋势: 'Execution trend',
  可用率: 'Availability',
  '磁盘I/O利用率': 'Disk I/O Utilization',
  响应时长top5: 'Top 5 Response Time',
  可用率top5: 'Top 5 Availability',
  CPU五分钟负载: 'CPU 5 Minute Load',
  '5分钟平均负载': '5 Minute Average Load',
  业务监控状态总览: 'Overview',
  主机性能状态分布: 'Distribution of Host Performance Status',
  主机监控异常报告: 'Host Monitoring - Abnormal',
  主机监控很健康: 'Host Monitoring is Healthy',
  告警类型触发次数分布图: 'Distribution of alarm trigger counts by type',
  异常分值分布: 'Outlier score distribution',
  未恢复告警分布: 'Unrecovered alarm distribution',
  服务监控异常报告: 'Service Monitoring - Abnormal',
  服务监控很健康: 'Service Monitoring is Healthy',
  进程监控异常报告: 'Process Monitoring -  Abnormal',
  进程监控很健康: 'Process monitoring is healthy',
  拨测监控异常报告: 'Synthetic Monitoring - Abnormal',
  拨测监控很健康: 'Probe monitoring is healthy',

  // 选择类
  '启/停': 'On/Off',
  '个/行': ' item /row',
  '增/删': 'Add/Del',

  // 功能操作类
  自定义上报: 'Custom Report',
  数据源管理: 'Datasource Management',
  默认仪表盘: 'Default Dashboard',
  新业务接入: 'New Business',
  自愈覆盖率: 'Self-Healing',
  建议您关注: 'Please pay attention',
  截图到本地: 'Screenshot to Local',
  Y轴自适应: 'Y-axis Adaptive',
  添加为监控: 'Add to Monitoring',
  复制指标名: 'Copy Metric',
  拨测任务组: 'Probe Task Group',
  解散任务组: 'Dissolve Task Group',
  自定义场景: 'Custom Scenario',
  自定义指标: 'Custom Metric',
  第三方ES: 'Third-party ES',

  // 表单只读态

  '节点:': 'Node:',
  '原因:': 'Reason:',
  '业务:': 'Business:',
  '所属:': 'Spaces:',
  '依赖:': 'Dependency:',
  '来源:': 'Source:',
  '主机名:': 'Hostname:',
  '最小值:': 'Min value:',
  '创建人:': 'Creator:',
  '敏感度:': 'Sensitivity:',
  '突增率:': 'Increase Rate:',
  '主机/进程': 'Host/Process',
  '国家/地区': 'Country/Region',
  '节点信息:': 'Node information:',
  '修改时间:': 'Modification time:',
  '维度信息:': 'Dimension Information:',
  '触发条件:': 'Trigger condition:',
  '通知次数:': 'Notifications:',
  '保存成功！': 'Saved Successfully!',
  '接口描述:': 'API Description:',
  '测试参数:': 'Test parameter:',
  '测试结果:': 'Test result:',
  '出错信息:': 'Error information:',
  '数据时间:': 'Data Time:',
  '指标/维度': 'Metrics/Dimensions',
  '参考例子:': 'Reference example: ',
  ' - 原始数据': ' - Raw Data',
  '正则匹配:': 'Regular matching: ',
  '范围匹配:': 'Range matching: ',

  Agent异常: 'Agent Exception',
  Agent正常: 'Agent Normal',

  // 告警表单
  当前值与前一时刻值: 'Current value compared to previous value',
  当前值较上一个时刻上升: 'Current value increases compared to the previous time',
  当前值较上一个时刻下降: 'Current value decreases compared to the previous time',
  当前值较上周同一时刻: 'Current value compared to the same time last week',
  当前值较上周同一时刻上升: 'Current value increases compared to the same time last week',
  当前值较上周同一时刻下降: 'Current value decreases compared to the same time last week',
  当前值较前一时刻: 'Current value compared to previous time',
  天同一时刻绝对值的均值: 'Mean absolute value at the same time of day',
  天同一时刻绝对值的均值上升: 'The mean value of the absolute value at the same time of day rises',
  天同一时刻绝对值的均值下降: 'The average absolute value at the same time of the day decreases by',
  当告警关闭时: 'When alarm is closed',
  当告警恢复时: 'When alarm recovers',
  当告警确认时: 'When alarm is acknowledged',
  当告警触发时: 'When alarm is triggered',
  当告警触发时执行: 'Execute when alarm is triggered',
  告警事件内容: 'Alarm Event Content',
  告警事件屏蔽: 'Alarm Event Mute',
  告警事件链接: 'Alarm Event Link',
  告警产生后是否要触发动作: 'Whether to trigger actions after the alarm is generated',
  告警产生后是否要触发通知: 'Whether to trigger a notification after an alarm is generated',
  告警产生时: 'When Alarm Generated',
  告警关闭时: 'Closed',
  告警名称列表: 'Alarm Name List',
  告警已关闭: 'Alarm Closed',
  告警已恢复: 'Alarm Actived',
  告警恢复时: 'Recovered',
  告警恢复时通知: 'Notify When Alarm Recovers',
  告警接收业务: 'Alarm receiving business',
  告警模板预览: 'Alarm Template Preview',
  告警确认时: 'Acknowledged',
  日志关键字事件: 'Log Keyword Event',
  日志关键字采集: 'Log keyword collection',
  '连续{0}个周期内不满足条件表示恢复': 'If the condition is not met for {0} consecutive cycles, it means recovery',
  '连续{0}个周期内不满足触发条件表示恢复':
    'If the trigger condition is not met for {0} consecutive cycles, it indicates recovery.',
  '间隔{0}分钟，逐个通知': 'Interval {0} minutes, notify one by one',
  单指标异常检测: 'Single metric abnormal detection',
  多指标计算: 'Multiple metric calculation',
  部分节点数: 'Partial Nodes',
  部分节点数算法: 'Partial Node Count Algorithm',
  前一时刻值: 'Previous Value',
  天内同时刻绝对值: 'Absolute value of the same time within {days} days', // 查看位置 不完整
  '满足以上条件的拨测节点数>={0}时触发告警':
    'Trigger an alarm when the number of dial test nodes that meet the above conditions is greater than or equal to {0}.',
  向上或向下: 'Up or Down',
  智能异常检测: 'Intelligent Anomaly Detection',
  智能异常检测算法: 'Intelligent anomaly detection algorithm',
  智能推荐指标: 'Smart recommendation metrics',
  敏感度区间: 'Sensitivity Interval',
  敏感度阈值: 'Sensitivity Threshold',
  最近变更时间: 'Recent Modification Time',
  '在{0}个周期内{1}满足{2}次检测算法触发异常告警':
    'Trigger an abnormal alarm when the detection algorithm is satisfied {2} times within {0} cycles.',
  '在{0}个周期内{1}满足{2}次检测算法，触发告警通知':
    '{1} meets {2} detection algorithms within {0} periods, triggering an alarm notification',
  '在{num}个周期内满足{count}次检测算法触发告警；':
    'Trigger an alarm when the detection algorithm is satisfied {count} times within {num} cycles.',
  第三方告警: 'Third-party Alarm',

  // 屏蔽
  '屏蔽开始/结束前': 'Before/after mute',
  '屏蔽开始/结束前{0}分钟发送通知': 'Send notification {0} minutes before/after the start/end of the shield',
  屏蔽时间剩余: 'Remaining',

  // 检索
  'Service 统计': 'Service Statistics',
  'TraceID 精确查询': 'TraceID Exact Query',
  '原始日志:': 'Original log:',

  '业务/集群/节点': 'Business/Cluster/Node',
  显示高级选项: 'Advanced',
  '采集配置名称/ID': 'Collection configuration name/ID',
  '主机&云平台': 'Host & Cloud Platform',
  指标选择器: 'Metric Selector',

  // 日历
  仅修改该日程: 'Only modify this schedule',
  仅删除该日程: 'Delete this schedule only',
  收藏至仪表盘: 'Add to Dashboard',

  // link
  K8s内置: 'K8s Built-in',
  Kubernetes: 'Kubernetes',
  Jinja2模板引擎: 'Jinja2 Template Engine',
  仪表盘视图: 'Dashboard View',

  // 分派
  触发的告警: 'Triggered Alarm',
  订阅人员列表: 'Subscriber List',
  设备IP列表: 'Device IP List',
  上下文名称: 'Context Name',
  snmp设备端口: 'SNMP Device Port',

  // 迁移
  原分组名称: 'Original Group Name',
  原监控源名称: 'Original Monitoring Source Name',
  原策略项名称: 'Original rule item name',
  原视图名称: 'Original View Name',
  迁移后分组名称: 'Group name after migration',
  迁移后策略名称: 'Rule Name after Migration',
  迁移后视图名称: 'View name after migration',

  // 自定义
  自动刷新设置: 'Auto Refresh Settings',
  自定义Exporter: 'Custom Exporter',
  自定义上报事件: 'Custom Reporting Events',
  自定义事件帮助: 'Custom Event Help',
  自定义字符型: 'Custom Character Type',
  自定义指标帮助: 'Custom metric help',
  自定义服务: 'Custom Service',
  自定义标签: 'Custom Tag',
  自定义监控视图: 'Custom monitoring view',
  自定义输入: 'Custom Input',

  // 告警
  异常指标数: 'Abnormal metrics',
  异常维度值: 'Unusual dimension value',
  异常维度值占比: 'Abnormal dimension Proportion',
  告警中的主机: 'Host in Alarm',
  实时告警事件: 'Real-time Alarm Events',
  时触发告警: 'Alarm triggered', // 查看位置，语义不明确
  有告警就执行: 'Execute when alarm acitved',
  通知告警组: 'Alarm Team',
  通知状态明细: 'Notification Status Details',
  告警级别调整: 'Modify Severity',
  告警触发时: 'Triggered',

  // 当前
  当前主机数: 'Hosts',
  当前实例数: 'Instances',
  采集器主机: 'Collector Host',
  所属任务组: 'Group',

  采集专有主机: 'Collector Host',
  采集器参数: 'Collector Parameters',
  采集配置文件: 'Collect configuration Files',
  '总时间（ms）': 'Total Time (ms)',
  标准化结果: 'Standardized Result',
  来自配置平台: 'From CMDB',
  多端日志匹配: 'Multi-end log matching',
  PaaS应用: 'PaaS App',

  链路管理: 'Pipeline Management',
  链路名称: 'Pipeline Name',
  使用范围: 'Range of work',
  投递到存储: 'Post to Storage:',
  投递到Kafka: 'Post to Kafka',
  是否默认: 'Default',
  是否可用: 'Available',
  数据更新时间: 'Data update time',
  请输入: 'Please enter',
  新增链路: 'Added Pipeline',
  Kafka链路: 'Kafka Pipeline',
  Transfer链路: 'Transfer Pipeline',
  是否为默认链路: 'Whether it is the default Pipeline',
  '已有默认链路{0}，如需更改需先去该链路下将其取消':
    'There is a default link {0}, if you need to change it, you need to cancel it under this Pipeline',
  数据投递: 'Data Delivery',
  投递到Influxdb: 'Post to Influxdb',
  备注说明: 'Note',
  空间筛选: 'Spaces',
  集群属性: 'set',
  模块属性: 'module',
  主机属性: 'host',
  耗时区间: 'Time-consuming interval',
  错误: 'Error',
  异常事件: 'Events',
  设为常用: 'Set as favorite',
  参照名称: 'Reference name',
  临时对比: 'Temporary comparison',
  常用参照: 'Common reference',
  按小时: 'By Hour',
  集群名称: 'Cluster name',
  集群域名: 'Cluster domain',
  访问协议: 'Access Protocol',
  ges注册配置: 'ges Registration Configuration',
  proxy集群域名: 'Proxy Cluster domain',
  ES集群管理: 'ES Cluster Management',
  ES地址: 'ES address',
  最大: 'Max',
  冷热数据: 'Hot and cold data',
  是否向GSE注册配置: 'Whether to register the configuration with the GSE',
  显示: 'Display',
  暂不对比: 'No comparison',
  '已达上限，请先删除后再新增': 'The upper limit has been reached, please delete and then add',
  通知类型: 'Notify Type'
};
