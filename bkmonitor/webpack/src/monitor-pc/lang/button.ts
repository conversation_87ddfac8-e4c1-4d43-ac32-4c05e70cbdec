/* eslint-disable codecc/comment-ratio */
/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * 蓝鲸智云PaaS平台 (BlueKing PaaS) available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * 蓝鲸智云PaaS平台 (BlueKing PaaS) is licensed under the MIT License.
 *
 * License for 蓝鲸智云PaaS平台 (BlueKing PaaS):
 *
 * ---------------------------------------------------
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and
 * to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of
 * the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO
 * THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
 * CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 */
export default {
  // 规范： 驼峰

  // - Button Text：按钮上的文本，用于描述该按钮的作用，例如“登录”、“注册”、“提交”等。
  // 通用表单操作类
  确认: 'Confirm',
  取消: 'Cancel',
  自动查询: 'Auto Query',
  查询: 'Query',
  清空: 'Clear',
  提交: 'Submit',
  复制IP: 'Copy IP',
  新建: 'New',
  导出列表: 'Export List',
  保存: 'Save',
  重置: 'Reset',
  测试: 'Test',
  'button-筛选': 'Filter',
  清除所有数据: 'Clear All',
  'button-完成': 'Complete',
  移除: 'Remove',
  添加: 'Add',
  再次调试: 'Debug Again',
  调试: 'Debug',
  'button-关闭': 'Close',
  下一步: 'Next',
  上一步: 'Previous',
  'button-调试中': 'Debugging',
  开始调试: 'Start Debugging',
  重新调试: 'Restart Debugging',
  导入: 'Import',
  导出: 'Export',
  返回: 'Back',
  开始下发: 'Deploy',
  回滚: 'Rollback',
  清除: 'Clear',
  删除: 'Delete',
  复位: 'Reset',
  复制链接: 'Copy Link',
  保存并匹配: 'Save & Match',
  'button-刷新': 'Refresh',
  'button-开始下发': 'Deploy',
  'button-说明': 'Description',
  强制保存: 'Force Save',
  关闭窗口: 'Close Window',
  复制目标: 'Copy Target',
  删除全部: 'Delete All',
  重新获取: 'Redownload',
  点击导出: 'Export',
  保存配置: 'Save Configuration',
  展开全部: 'Expand All',
  '展开更多 ({num})': 'Expand more ({num})',
  '收起更多 ({num})': 'Collapse more ({num})',
  展开后编辑: 'Edit after expansion',
  增加: 'Add',
  收起所有分组: 'Collapse All',
  展开所有分组: 'Expand All',
  统一设置: 'Unified Setting',
  格式化: 'Format',
  管理: 'Manage',
  创建副本: 'Duplicate',
  'button-编辑': 'Edit',
  调试并生效: 'Debug & Save',
  调试并删除: 'Debug & Delete',
  创建流程: 'Create Process',
  收起: 'Collapse',
  展开: 'Expand',
  退出登录: 'Sign out',
  产品文档: 'Documentation',
  版本日志: 'Release note',
  问题反馈: 'Feedback',
  操作: 'Actions',
  'Trace 视角': 'Trace List',
  'Span 视角': 'Span List',
  一键转换: 'One-click conversion',
  批量删除: 'Bulk Delete',
  批量回收: 'Bulk Recycl',
  批量撤回: 'Bulk Reset',
  批量操作: 'Bulk Operate',
  批量终止: 'Bulk Termination',
  批量重试: 'Bulk Retry',
  批量确认: 'Bulk Confirm',
  批量分派: 'Bulk Routing',
  批量屏蔽: 'Bulk Mute',
  新建组: 'New Group',
  自动转换: 'Auto Conversion',
  修改后重试: 'Retry after modification',

  // 规范：平台特性 术语
  新建节点: 'New Probe',
  新建拨测: 'New Check',
  新建任务组: 'New Check Group',
  导入拨测任务: 'Import Check',
  新建集群: 'New Cluster',
  导入仪表盘: 'Import Dashboard',
  导入告警策略: 'Import Alert Rules',
  新建映射规则: 'New Mapping Rule',
  删除所有将来日程: 'Delete All Future Schedules',
  编辑策略: 'Edit Alert Rule',
  点击解析: 'Parse',
  添加至列表: 'Add to List',
  新增组: 'Group',
  添加套餐: 'Add Solutions',
  保存套餐: 'Save Solutions',
  更新插件: 'Update Plugin',
  创建插件: 'New Plugin',
  更新至现有插件: 'Update Plugins',
  前往采集配置升级插件: 'Upgrade Plugin',
  新建采集配置: 'New Collection',
  此刷新仅追加新获取的指标和维度: 'Only Append new metrics and dimensions',
  新增指标: 'Add Metric',
  新增维度: 'Add Dimension',
  新增用户配置: 'Add Item',
  添加策略: 'Add Alert Rules',
  新建分组: 'New Group',
  '移动到...': 'Move to...',
  移动至分组: 'Move to Group',
  分组管理: 'Group Management',
  管理历史分享: 'Sharing History',
  停用旧版监控策略: 'Disable Old Rules',
  仅创建默认策略: 'Create Default Rule',
  添加用户组: 'Add User Group',
  加进分组: 'Add to Group',
  从该组移除: 'Remove from Group',
  合并到日历: 'Merge to Calendar',
  'button-添加内容': 'Add Content',
  'button-开启APM': 'Open APM',
  'button-自定义': 'Customize',
  'button-主机': 'Host',
  'button-服务实例': 'Service Instances',
  可视化: 'Visualize',
  'button-策略配置': 'Rules',
  'button-预览': 'Preview',
  'button-字段': 'Field',
  'button-标签': 'Label',
  'button-业务': 'Business',
  'button-拓扑节点': 'Topology Node',
  关闭通知: 'Close Notification',
  直接通知: 'Direct Notification',
  配置规则: 'Configuration Rules',
  新建订阅: 'New Subscription',
  上传文件: 'Upload File',
  上传内容: 'Upload Content',
  修改时间: 'Mofidy Time',
  修改标签: 'Modify Tag',
  修改目标: 'Modify Target',
  创建分组: 'Create Group',
  创建应用: 'Create Application',
  创建状态: 'Creation Status',
  创建策略: 'Create Rule',
  创建记录: 'Create Record',
  删除策略: 'Delete Rule',
  添加URL: 'Add URL',
  添加事件: 'Add Event',
  添加内容: 'Add Content',
  添加静态IP: 'Add Static IP',
  添加图表: 'Add Chart',
  添加域名: 'Add Domain',
  添加子级: 'Add Sub-level',
  添加字段: 'Add Field',
  添加对象: 'Add Object',
  添加指标: 'Add Metrics',
  添加条件: 'Add Condition',
  添加用户: 'Add User',
  添加目标: 'Add Target',
  新增: 'Add',
  新增目录: 'Add Directory',
  新增事项: 'Add Item',
  新增页签: 'Tab',
  新增分组: 'Group',
  新增收藏: 'Add to Favorites',
  新增服务: 'Add Service',
  新增告警组: 'New Alarm Team',
  新增处理建议: 'Add Suggestions',
  新增无数据告警: 'New No-Data Alarm',
  新增消息通知渠道: 'Add Notification Channel',
  新增后会进行回填: 'Auto-Filling will be done',
  新建仪表盘: 'New Dashboard',
  新建目录: 'New Directory',
  新建拨测任务: 'New Probe Task',
  新建拨测节点: 'New Probe Node',
  新建容器项目: 'New BCS Project',
  新建拨测任务组: 'Create Group',
  新建自定义事件: 'New Custom Event',
  新建自定义指标: 'New Custom Metric',
  新建自定义服务: 'New Custom Service',
  指标设置: 'Metric Settings',

  // 标签类
  'button-执行中': 'Running',
  '执行中...': 'Executing...',
  下发中: 'Downloading', // 查看语境

  // - Tab Text：选项卡文本，用于描述选项卡的作用，例如“基本信息”、“高级设置”等。

  // - Call to Action Text：行动号召文本，用于鼓励用户进行某种行动，例如“立即注册”、“立即下载”、“立即购买”等。
  'button-接入指引': 'Guide',
  去申请: 'Go to Apply',
  接入业务: 'New Business',
  申请权限: 'Apply Permission',
  我要体验: 'Try it',
  DEMO: 'DEMO',
  接入主机: 'Add Hosts',
  新建数据采集: 'New Collection',
  开始自定义: 'Start Customizing',
  接入Kubernetes: 'New Kubernetes',
  暂不添加: 'Skip',
  接入服务: 'New Service',
  新建应用: 'New Application',
  开始迁移: 'Start Migration',
  安装: 'Install ',
  前往添加统一监控目标: 'Add Unified Target',
  立即查看: 'View Now',
  立即接入: 'Do Now',
  立即配置: 'Configure Now',
  立即创建: 'Create Now',
  立即录入: 'Enter Now',
  立即部署: 'Deploy Now',
  立即开启: 'Open Now',
  立即导入: 'Import Now',
  立即新建: 'Create Now',
  点击这里: 'Click here',
  前往添加: 'Go to Add',
  前往查看: 'Go to View',
  前往设置: 'Go to Settings',
  去新建: 'Go to Create',
  先申请吧: 'Go to Apply',
  '知道了!': 'Got it!',
  '数据采集好了，去 {0}': 'Data is ready, go to {0}',
  '插件制作好了，去 {0}': 'Plugin is ready, go to {0}',
  '数据上报好了，去 {0}': 'Data is ready, go to {0}',
  马上了解: 'Learn Now',
  返回插件定义: 'Return to Definition',
  返回调试: 'Return to Debug',
  前往日志检索: 'Go to Log Explore',
  前往告警列表: 'Go to Alarm List',
  前往CMDB: 'Go to CMDB',
  前往配置策略: 'Go to Configure',
  前往日历服务: 'Go to Calendar',
  前往文档中心: 'Go to Documentation Center',
  到指标维度设置: 'Go to Settings',
  前往节点管理处理: 'Go to NodeMan',
  前往选择监控目标: 'Go to Select Targets',
  采集器安装前往节点管理: 'Go to NodeMan',

  // - Tool Text：工具栏文本，用于描述工具栏按钮的作用，例如“新建”、“保存”等。
  // 图表操作类
  收藏并跳转: 'Add & View',
  直接收藏: 'Add to Dashboard',
  'button-指标对比': 'Compare',
  合并视图: 'Merge',
  收藏: 'Favorite',
  表达式: 'Expression',
  查询项: 'Query Item',
  'button-表达式': 'Expression',
  'button-查看': 'View',
  一列: '1 col',
  两列: '2 cols',
  三列: '3 cols',
  四列: '4 cols',
  五列: '5 cols',
  对比: 'Compare',
  平铺: 'Tile',

  实例: 'Instance',
  效果调试: 'Debug',
  删除并调试: 'Delete & Debug',
  根Span: 'Trace Root Spans',
  入口Span: 'Service Entry Spans',
  生成自定义指标: 'Create custom metric',
  接口统计: 'Interface Statistics',
  服务统计: 'Service Statistics',
  去观测: 'Observe',
  添加授权: 'Add',
  编辑授权: 'Edit',
  变更授权人: 'Change Auth Person',

  // 查看
  查看变更记录: 'Changelog',
  查看上报日志: 'View Reporting Log',
  查看上报数据: 'View Reported Data',
  查看分派规则: 'View',
  查看数据: 'View Data',
  查看原始数据: 'View Raw Data',
  查看变更历史: 'View Change History',
  查看告警列表: 'View Alarms',
  查看屏蔽策略: 'View Mute',
  查看更多相关的事件: 'More Related Event',
  查看更多相关的数据: 'More Related Data',
  查看更多相关的日志: 'More Related Log',
  查看更多语法规则: 'More Grammar',
  查看监控目标: 'View Scope',
  查看迁移内容: 'View Migration',
  查看风险点: 'View Risk',
  查看更多文档: 'More Documentations',
  更多敏感度区间设置: 'More Sensitivity',

  // 点击
  '简介怎么是空的！ 点击查看具体详情吧。': 'The introduction is empty! Click to view the details.',
  点击上传mib转换后的yaml配置文件: 'Click to upload the YAML configuration file converted from MIB',
  点击上传文件: 'Upload files',
  点击展开全部: 'Expand All',
  点击收起收藏: 'Close Favorite',
  点击查看推荐变量: 'Recommended Variables',
  点击查看明细: 'Details',
  点击添加参数: 'Add parameters',
  点击选择或拖拽文件至此: 'Select or Drag File',
  点击选择测试目标: 'Select Test Probe',
  点击隐藏默认: 'Hide default',

  // 添加
  添加一级标签: 'Add primary label',
  添加告警组: 'Add',
  添加拨测任务: 'Add Probe Task',
  添加日志关键字: 'Add Log Keyword',
  添加更多条件: 'Add More Conditions',
  添加查询语句: 'add query statement',
  添加检测算法: 'Add detection algorithm',
  添加监控指标: 'Add Metrics',
  添加监控目标: 'Change Scope',
  添加监控策略: 'Add Alert Rule',
  添加监控项: 'Add Monitoring Item',
  添加采集插件运行主机: 'Add Hosts for Running Collection Plugins',

  // 编辑
  编辑告警组: 'Edit',
  编辑拨测任务: 'Edit Probe Task',
  编辑拨测任务组: 'Edit Group',
  编辑拨测节点: 'Edit Probe Node',
  编辑指标分类: 'Edit Metric Category',

  // 开启
  开启APM: 'Start APM',
  开启Kubernetes监控: 'Start Kubernetes Monitoring',
  开启容器监控: 'Start Container Monitoring',
  开启综合拨测: 'Start Synthetic',

  '启/停策略': 'On/Off Rule',
  开启周期回调: 'Periodic Callback',

  // 导出
  导出CSV: 'Export CSV',
  导出Yaml: 'Export Yaml',

  // 设置
  设为公共插件: 'Public Plugin',
  设为公共节点: 'Public Node',

  // 清空
  清空搜索条件: 'Clear search conditions',
  清空筛选条件: 'Clear filtering',

  快捷屏蔽告警: 'Mute', // 确定这个词条位置
  快捷屏蔽告警事件: 'Mute Alarm Events',
  快捷屏蔽策略: 'Mute Alert Rule',

  // 请点击

  保存到仪表盘: 'Save to Dashboard',
  停用该策略: 'Disable Rule',
  停用该采集任务: 'Disable Collection Task',
  停用采集配置: 'Disable Collection',
  关联告警策略: 'Related Rule',

  // link
  操作系统事件: 'OS Events',
  操作系统指标: 'OS Metrics',
  进程指标: 'Process Metrics',
  开始数据采集: 'Start',
  申请业务权限: 'Apply for Business Permissions',
  跳转至主机监控: 'Jump to Host Monitoring',
  跳转至自定义场景: 'Jump to Custom Scene',
  字段显示设置: 'Display Settings',
  增加指标分类: 'Add Metrics Classification',
  相关告警: 'Related Alarms',
  相关文档查看: 'View Documents',
  相关进程信息: 'Related Process',
  启用采集配置: 'Enable Collection Configuration',
  前去升级配置: 'Go Upgrade Configuration',
  数据源配置: 'Data Source Configuration',
  进程配置: 'Process Configuration',
  Span检索: 'Span search',
  观测: 'Observe',

  更新执行历史: 'Update Execution History',
  告警分析: 'Analysis',
  关联事件: 'Related Events',
  告警确认: 'Confirm',
  解除: 'Unmute',
  拉群: 'WeCom',
  连通性测试: 'Connectivity test',
  更多配置: 'More configuration',
  新增群组: 'Add Group'
};
