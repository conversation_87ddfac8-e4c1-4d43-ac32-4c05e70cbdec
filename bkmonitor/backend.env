export BK_HOME="/Users/<USER>/workspace/bk-monitor/bkmonitor"
export DJANGO_CONF_MODULE="conf.worker.production.enterprise"
export OPENSSL_CONF="/dev/null"
export DJANGO_SETTINGS_MODULE="settings"
export BKAPP_DEPLOY_PLATFORM="enterprise"
export DEPLOY_MODE="kubernetes"
export BK_PAAS_HOST="http://paas.ccops.bcopstest.com"
export APP_CODE="bk_monitorv3"
export APP_TOKEN="cb31403e-055c-4c12-a9af-df5a76e4a561"
export BK_MONITOR_APP_CODE="bk_monitorv3"
export BK_MONITOR_APP_SECRET="cb31403e-055c-4c12-a9af-df5a76e4a561"
export BK_COMPONENT_API_URL="http://bkapi.ccops.bcopstest.com"
export BKAPP_LOG_LEVEL="INFO"
export BK_IAM_SYSTEM_ID="bk_monitorv3"
# 内部服务，需要修改为IP访问
export BK_IAM_V3_INNER_HOST="http://bkiam-api.ccops.bcopstest.com"
export BK_IAM_MIGRATION_JSON_PATH="iam/"
# 内部服务，需要修改为IP访问
export BKAPP_IAM_RESOURCE_API_HOST="http://bk-monitor-web/"
export BK_SSM_HOST="http://bkssm.ccops.bcopstest.com"
export BK_SSM_PORT="80"
export BK_GSE_ZK_HOST="bk-gse-zookeeper-headless"
export BK_GSE_ZK_PORT="2181"
export BK_BCS_URL="http://bcs.ccops.bcopstest.com/"
export BK_IAM_SITE_URL="http://bkiam.ccops.bcopstest.com"
export BK_DOCS_SITE_URL="http://apps.ccops.bcopstest.com/bk-docs-center/"
export BK_LOG_SEARCH_SITE_URL="http://bklog.ccops.bcopstest.com/"
export BK_CC_SITE_URL="http://cmdb.ccops.bcopstest.com/"
export BK_JOB_SITE_URL="http://job.ccops.bcopstest.com/"
export BK_NODEMAN_SITE_URL="http://bknodeman.ccops.bcopstest.com"
# 内部服务，需要修改为IP访问
export BKAPP_GRAFANA_URL="http://bk-monitor-grafana:3000/"
export BK_ITSM_HOST="http://apps.ccops.bcopstest.com/bk-itsm/"
export BK_MONITOR_HOST="http://bkmonitor.ccops.bcopstest.com/"
export BK_SOPS_URL="http://apps.ccops.bcopstest.com/bk-sops/"
# 内部服务，需要修改为IP访问
export BKAPP_BCS_CC_API_URL="http://bcs-ui-cc.bcs-system.svc.cluster.local:5000"
# export BKAPP_BCS_CC_API_URL="http://bcs.ccops.bcopstest.com:5000"
export BKAPP_BCS_API_GATEWAY_TOKEN="pPgJcfMk0d3HYFuJB1HDvUTjlBX89S67"
export BKAPP_BCS_API_GATEWAY_HOST="bcs-api-gateway.bcs-system.svc.cluster.local"
export BKAPP_BCS_API_GATEWAY_HOST="bcs-api.ccops.bcopstest.com"
export BKAPP_BCS_API_GATEWAY_PORT="80"
export BKAPP_BCS_API_GATEWAY_SCHEMA="http"
export BK_BCS_CLUSTER_SOURCE="cluster-manager"
export USE_BKREPO="true"
export UPLOAD_PLUGIN_VIA_COS="true"
export BKREPO_ENDPOINT_URL="http://bkrepo.ccops.bcopstest.com"
export BKREPO_USERNAME="bkmonitor"
export BKREPO_PASSWORD="G9MfNa7_xR1U"
export BKREPO_PROJECT="blueking"
export BKREPO_BUCKET="bkmonitor"

# 本地Mysql数据库
export BKAPP_SAAS_DB_PASSWORD="12345678"
export BKAPP_SAAS_DB_PORT="3306"
export BKAPP_SAAS_DB_USER="root"
export BKAPP_SAAS_DB_HOST="127.0.0.1"
export BK_MONITOR_MYSQL_HOST="127.0.0.1"
export BK_MONITOR_MYSQL_PASSWORD="12345678"
export BK_MONITOR_MYSQL_PORT="3306"
export BK_MONITOR_MYSQL_USER="root"
export BK_MONITOR_MYSQL_NAME="bk_monitor"
export DB_NAME="bk_monitor"


# 本地后台rabbitmq
export BK_MONITOR_RABBITMQ_VHOST="bk_monitor"
export BK_MONITOR_RABBITMQ_PORT="5672"
export BK_MONITOR_RABBITMQ_PASSWORD="guest"
export BK_MONITOR_RABBITMQ_USERNAME="guest"
export BK_MONITOR_RABBITMQ_HOST="127.0.0.1"


# 本地redis配置
export REDIS_PASSWORD="foobared"
export REDIS_HOST="localhost"
export REDIS_PORT=6379

export BK_MONITOR_UNIFY_QUERY_HOST="unify-query.ccops.bcopstest.com"
export BK_MONITOR_UNIFY_QUERY_PORT="80"

# 本地
export BKAPP_BCS_API_BASE_URL="bcs-api.ccops.bcopstest.com"
export BKAPP_BCS_API_SCHEMA="http"
export BKAPP_BCS_API_PORT=80