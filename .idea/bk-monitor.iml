<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="FacetManager">
    <facet type="django" name="Django">
      <configuration>
        <option name="rootFolder" value="$MODULE_DIR$/bkmonitor/packages/monitor_web/grafana/resources" />
        <option name="settingsModule" value="settings.py" />
        <option name="manageScript" value="manage.py" />
        <option name="environment" value="&lt;map/&gt;" />
        <option name="doNotUseTestRunner" value="false" />
        <option name="trackFilePattern" value="migrations" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/alarm_backends" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/metadata" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/packages/monitor_web" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/bkm_ipchooser" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/bkmonitor" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/packages/monitor" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/kernel_api" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/query_api" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/bkmonitor/bk_dataview" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/monitor_venv" />
    </content>
    <orderEntry type="jdk" jdkName="Python 3.6 (bk-monitor)" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
  <component name="TemplatesService">
    <option name="TEMPLATE_CONFIGURATION" value="Jinja2" />
    <option name="TEMPLATE_FOLDERS">
      <list>
        <option value="$MODULE_DIR$/bkmonitor/bkmonitor/templates" />
      </list>
    </option>
  </component>
  <component name="TestRunnerService">
    <option name="PROJECT_TEST_RUNNER" value="py.test" />
  </component>
</module>