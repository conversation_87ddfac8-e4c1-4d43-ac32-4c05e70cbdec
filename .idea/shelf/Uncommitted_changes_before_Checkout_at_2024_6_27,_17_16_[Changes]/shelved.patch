Index: collect_process/collect_process.py
===================================================================
diff --git a/collect_process/collect_process.py b/collect_process/collect_process.py
--- a/collect_process/collect_process.py	
+++ b/collect_process/collect_process.py	
@@ -32,8 +32,12 @@
     :param tar_gz_path: .tar.gz文件的路径
     :param destination_dir: 解压目标目录
     """
-    with tarfile.open(tar_gz_path, "r:gz") as tar:
-        tar.extractall(path=destination_dir)
+    try:
+        with tarfile.open(tar_gz_path, "r:gz") as tar:
+            tar.extractall(path=destination_dir)
+    except Exception as e:
+        f = open("result.txt", "w")
+        f.write(tar_gz_path + "\n")
 
 
 if __name__ == "__main__":
Index: collect_process/deploy_collect.py
===================================================================
diff --git a/collect_process/deploy_collect.py b/collect_process/deploy_collect.py
--- a/collect_process/deploy_collect.py	
+++ b/collect_process/deploy_collect.py	
@@ -27,6 +27,7 @@
 parser.add_argument("--collect_dir", type=str, default="./collect/collect_unzip")
 parser.add_argument("--max_collect_id", type=int, default=300)
 
+
 def read_collect(collect_dir):
     filenames = []
     for root, dirs, files in os.walk(collect_dir):
@@ -64,13 +65,14 @@
     bk_csrftoken = [str(res) for res in cookie.split('; ') if res.startswith('bk_csrftoken')][0]
     return cookie, bk_csrftoken
 
+
 def save_collect_config(data):
     """
     采集下发
     """
     save_url = os.path.join(config["base_url"], "rest/v2/collecting_config/save/")
     headers = {
-        "User-Agent":  config["User-Agent"],
+        "User-Agent": config["User-Agent"],
         "Content-Type": config["Content-Type"],
         "Cookie": config["Cookie"],
         "X-Csrftoken": config["X-Csrftoken"]
@@ -107,6 +109,7 @@
             save_collect_config(data)
         # print(data)
 
+
 if __name__ == '__main__':
     args = parser.parse_args()
     filenames = read_collect(args.collect_dir)
Index: collect_process/result.txt
===================================================================
diff --git a/collect_process/result.txt b/collect_process/result.txt
--- a/collect_process/result.txt	
+++ b/collect_process/result.txt	
@@ -1,1 +1,1 @@
-352
+./collect/collect_zip/.DS_Store
