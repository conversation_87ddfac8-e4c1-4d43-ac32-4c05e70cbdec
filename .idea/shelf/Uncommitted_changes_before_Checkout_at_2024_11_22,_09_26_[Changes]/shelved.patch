Index: bkmonitor/alarm_backends/core/storage/kafka.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/bkmonitor/alarm_backends/core/storage/kafka.py b/bkmonitor/alarm_backends/core/storage/kafka.py
--- a/bkmonitor/alarm_backends/core/storage/kafka.py	
+++ b/bkmonitor/alarm_backends/core/storage/kafka.py	
@@ -172,7 +172,9 @@
                 if message_count >= count:  # 获取10条消息后退出
                     break
         except KeyboardInterrupt:
-            print("消费中断")
+            logger.info("kafka consumer encounter a problems!!!")
+        except Exception as e:
+            logger.info("kafka exception: ", e)
         finally:
             if settings.KAFKA_AUTO_COMMIT:
                 if self.get_consumer().commit() is False:
@@ -197,7 +199,8 @@
 
     @property
     def key(self):
-        return "_".join(map(str, [self.KEY_PREFIX, self.consumer.config["group_id"]]))
+        return "_".join(map(str, [self.KEY_PREFIX, self.consumer.group, self.consumer.topic]))
+        # return "_".join(map(str, [self.KEY_PREFIX, self.consumer.config["group_id"]]))
 
     @property
     def reset_key(self):
