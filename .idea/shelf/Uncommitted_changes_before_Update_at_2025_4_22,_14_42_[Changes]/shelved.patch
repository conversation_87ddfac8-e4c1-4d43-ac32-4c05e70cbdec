Index: bkmonitor/config/role/worker.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># -*- coding: utf-8 -*-\n\"\"\"\nTencent is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.\nCopyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.\nLicensed under the MIT License (the \"License\"); you may not use this file except in compliance with the License.\nYou may obtain a copy of the License at http://opensource.org/licenses/MIT\nUnless required by applicable law or agreed to in writing, software distributed under the License is distributed on\nan \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the\nspecific language governing permissions and limitations under the License.\n\"\"\"\nimport logging\nimport os\nimport sys\n\nfrom jinja2 import DebugUndefined\n\nfrom config.tools.consul import get_consul_settings\nfrom config.tools.rabbitmq import get_rabbitmq_settings\nfrom config.tools.redis import get_redis_settings\nfrom bkmonitor.utils.rsa.crypto import crypto_util\n\nfrom ..tools.environment import (\n    DJANGO_CONF_MODULE,\n    ENVIRONMENT,\n    IS_CONTAINER_MODE,\n    NEW_ENV,\n    ROLE,\n)\n\n# 按照环境变量中的配置，加载对应的配置文件\ntry:\n    _module = __import__(f\"config.{NEW_ENV}\", globals(), locals(), [\"*\"])\nexcept ImportError as e:\n    logging.exception(e)\n    raise ImportError(\"Could not import config '{}' (Is it on sys.path?): {}\".format(DJANGO_CONF_MODULE, e))\n\nfor _setting in dir(_module):\n    if _setting == _setting.upper():\n        locals()[_setting] = getattr(_module, _setting)\n\nROOT_URLCONF = \"alarm_backends.urls\"\n\n# SUPERVISOR 配置\nSUPERVISOR_PORT = 9001\nSUPERVISOR_SERVER = \"unix:///var/run/bkmonitorv3/monitor-supervisor.sock\"\nSUPERVISOR_USERNAME = \"\"\nSUPERVISOR_PASSWORD = \"\"\n\nINSTALLED_APPS += (  # noqa: F405\n    \"django_elasticsearch_dsl\",\n    \"django_jinja\",\n    \"bkmonitor\",\n    \"bkm_space\",\n    \"calendars\",\n    \"metadata\",\n    \"alarm_backends\",\n    \"apm\",\n    \"core.drf_resource\",\n)\n\n# 系统名称\nBACKEND_NAME = \"BK Monitor Backend\"\n\n# access最大向前拉取事件, second\nMIN_DATA_ACCESS_CHECKPOINT = 30 * 60\n# access 每次往前多拉取1个周期的数据\nNUM_OF_COUNT_FREQ_ACCESS = 1\n\n# 流控配置\nQOS_DROP_ALARM_THREADHOLD = 3\n\nTEMPLATES = [\n    {\n        \"NAME\": \"jinja2\",\n        \"BACKEND\": \"django_jinja.backend.Jinja2\",\n        \"DIRS\": [\"templates\"],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"match_extension\": \".jinja\",\n            \"context_processors\": [\n                \"django.template.context_processors.i18n\",\n                \"django.contrib.messages.context_processors.messages\",\n            ],\n            \"undefined\": DebugUndefined,\n        },\n    },\n    {\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\n        \"DIRS\": [],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"context_processors\": [\n                \"django.template.context_processors.i18n\",\n                \"django.contrib.messages.context_processors.messages\",\n            ],\n        },\n    },\n]\n\n# 是否启用通知出图\nGRAPH_RENDER_SERVICE_ENABLED = True\n\n# 告警检测范围动态关联开关\nDETECT_RANGE_DYNAMIC_ASSOCIATE = True\n\n# 告警开关\nENABLE_PING_ALARM = True\nENABLE_AGENT_ALARM = True\nENABLE_DIRECT_AREA_PING_COLLECT = True  # 是否开启直连区域的PING采集\n\nHEALTHZ_ALARM_CONFIG = {}\n\n# CRONTAB\nDEFAULT_CRONTAB = [\n    # eg:\n    # (module_name, every) like: (\"fta.poll_alarm.main start\", \"* * * * *\")\n    # Notice Notice Notice:\n    # Use UTC's time zone to set your crontab instead of the local time zone\n    # cmdb cache\n    (\"alarm_backends.core.cache.cmdb.host\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.module\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.set\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.business\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.service_instance\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.topo\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.service_template\", \"*/30 * * * *\"),\n    (\"alarm_backends.core.cache.cmdb.set_template\", \"*/30 * * * *\"),\n    # model cache\n    (\"alarm_backends.core.cache.strategy\", \"* * * * *\"),\n    (\"alarm_backends.core.cache.shield\", \"* * * * *\"),\n    (\"alarm_backends.core.cache.models.collect_config\", \"* * * * *\"),\n    (\"alarm_backends.core.cache.models.uptimecheck\", \"* * * * *\"),\n    (\"alarm_backends.core.cache.action_config\", \"* * * * *\"),\n    (\"alarm_backends.core.cache.assign\", \"* * * * *\"),\n    (\"alarm_backends.core.cache.calendar\", \"* * * * *\"),\n    # api cache\n    (\"alarm_backends.core.cache.result_table\", \"*/10 * * * *\"),\n    # hash ring result\n    (\"alarm_backends.core.cache.hash_ring\", \"* * * * *\"),\n    # delay queue\n    (\"alarm_backends.core.cache.delay_queue\", \"* * * * *\"),\n    # bcs cluster cache\n    (\"alarm_backends.core.cache.bcs_cluster\", \"*/10 * * * *\"),\n    # clean detect result cache\n    (\"alarm_backends.core.detect_result.tasks.clean_expired_detect_result\", \"0 */2 * * *\"),\n    (\"alarm_backends.core.detect_result.tasks.clean_md5_to_dimension_cache\", \"0 23 * * *\"),\n    # 定期清理超时未执行任务\n    (\"alarm_backends.service.fta_action.tasks.check_timeout_actions\", \"* * * * *\"),\n    # 定期清理mysql内半个月前的数据\n    (\"alarm_backends.service.fta_action.tasks.clear_mysql_action_data\", \"* * * * *\"),\n    (\"bkmonitor.documents.tasks.rollover_indices\", \"*/10 * * * *\"),\n    # mail_report 配置管理和告警接收人信息缓存\n    (\"alarm_backends.core.cache.mail_report\", \"*/30 * * * *\"),\n    # apm topo discover: 每分钟触发，每次分片处理1/10应用\n    (\"apm.task.tasks.topo_discover_cron\", \"* * * * *\"),\n    # apm 配置下发: 每分钟触发，每次分片处理1/30应用\n    (\"apm.task.tasks.refresh_apm_config\", \"* * * * *\"),\n    (\"apm.task.tasks.refresh_apm_platform_config\", \"*/30 * * * *\"),\n    # apm 检测预计算表字段是否有更新 1小时执行检测一次\n    (\"apm.task.tasks.check_pre_calculate_fields_update\", \"0 */1 * * *\"),\n]\n\nif BCS_API_GATEWAY_HOST:\n    DEFAULT_CRONTAB += [\n        # bcs资源同步\n        (\"api.bcs.tasks.sync_bcs_cluster_to_db\", \"*/10 * * * *\"),\n        (\"api.bcs.tasks.sync_bcs_service_to_db\", \"*/10 * * * *\"),\n        (\"api.bcs.tasks.sync_bcs_workload_to_db\", \"*/10 * * * *\"),\n        (\"api.bcs.tasks.sync_bcs_pod_to_db\", \"*/10 * * * *\"),\n        (\"api.bcs.tasks.sync_bcs_node_to_db\", \"*/10 * * * *\"),\n        (\"api.bcs.tasks.sync_bcs_service_monitor_to_db\", \"*/10 * * * *\"),\n        (\"api.bcs.tasks.sync_bcs_pod_monitor_to_db\", \"*/10 * * * *\"),\n        # bcs资源数据状态同步\n        # TODO: 调整好后再开启\n        # (\"api.bcs.tasks.sync_bcs_cluster_resource\", \"*/5 * * * *\"),\n        # (\"api.bcs.tasks.sync_bcs_workload_resource\", \"*/5 * * * *\"),\n        # (\"api.bcs.tasks.sync_bcs_service_resource\", \"*/5 * * * *\"),\n        # (\"api.bcs.tasks.sync_bcs_pod_resource\", \"*/5 * * * *\"),\n        # (\"api.bcs.tasks.sync_bcs_container_resource\", \"*/5 * * * *\"),\n        # (\"api.bcs.tasks.sync_bcs_node_resource\", \"*/5 * * * *\"),\n        # bcs集群安装operator信息，一天同步一次\n        (\"api.bcs.tasks.sync_bkmonitor_operator_info\", \"0 2 * * *\"),\n    ]\n\nACTION_TASK_CRONTAB = [\n    # 定期检测异常告警\n    (\"alarm_backends.service.alert.manager.tasks.check_abnormal_alert\", \"* * * * *\"),\n    # 定期关闭流控告警，避免与整点之类的任务并发，设置每12分钟执行一次\n    (\"alarm_backends.service.alert.manager.tasks.check_blocked_alert\", \"*/12 * * * *\"),\n    # 定期检测屏蔽策略，进行告警的屏蔽\n    (\"alarm_backends.service.converge.shield.tasks.check_and_send_shield_notice\", \"* * * * *\"),\n    # 定期同步数据至es\n    (\"alarm_backends.service.fta_action.tasks.sync_action_instances\", \"* * * * *\"),\n    # 定期维护排班计划\n    (\"alarm_backends.service.fta_action.tasks.generate_duty_plan_task\", \"* * * * *\"),\n    # 定期巡检周期通知和回调任务\n    (\"alarm_backends.service.fta_action.tasks.check_create_poll_action\", \"* * * * *\"),\n    # 定期处理demo任务\n    (\"alarm_backends.service.fta_action.tasks.dispatch_demo_action_tasks\", \"* * * * *\"),\n]\n\nif os.getenv(\"DISABLE_METADATA_TASK\") != \"True\":\n    DEFAULT_CRONTAB += [\n        # metadata\n        # metadata更新每个influxdb的存储RP，UTC时间的22点进行更新，待0点influxdb进行清理\n        (\"metadata.task.refresh_default_rp\", \"0 22 * * *\"),\n        # metadata同步pingserver配置，下发iplist到proxy机器，每10分钟执行一次\n        (\"metadata.task.ping_server.refresh_ping_server_2_node_man\", \"*/10 * * * *\"),\n        # metadata同步自定义上报配置到节点管理，完成配置订阅，理论上，在配置变更的时候，会执行一次，所以这里运行周期可以放大\n        (\"metadata.task.custom_report.refresh_custom_report_2_node_man\", \"*/5 * * * *\"),\n        # metadata自动部署bkmonitorproxy\n        (\"metadata.task.auto_deploy_proxy\", \"30 */2 * * *\"),\n        (\"metadata.task.config_refresh.refresh_kafka_storage\", \"*/10 * * * *\"),\n        (\"metadata.task.config_refresh.refresh_kafka_topic_info\", \"*/10 * * * *\"),\n        (\"metadata.task.config_refresh.refresh_consul_es_info\", \"*/10 * * * *\"),\n        (\"metadata.task.config_refresh.refresh_consul_storage\", \"*/10 * * * *\"),\n        (\"metadata.task.config_refresh.refresh_bcs_info\", \"*/10 * * * *\"),\n        # 刷新metadata降精度配置，10分钟一次\n        (\"metadata.task.downsampled.refresh_influxdb_downsampled\", \"*/10 * * * *\"),\n        # 降精度计算，5分钟一次检测一次\n        (\"metadata.task.downsampled.access_and_calc_for_downsample\", \"*/5 * * * *\"),\n        # 刷新回溯配置\n        (\"metadata.task.config_refresh.refresh_es_restore\", \"* * * * *\"),\n        # bcs信息刷新\n        (\"metadata.task.bcs.refresh_bcs_monitor_info\", \"*/10 * * * *\"),\n        (\"metadata.task.bcs.refresh_bcs_metrics_label\", \"*/10 * * * *\"),\n        (\"metadata.task.bcs.discover_bcs_clusters\", \"*/10 * * * *\"),\n        # 同步空间信息\n        (\"metadata.task.sync_space.sync_bkcc_space\", \"*/10 * * * *\"),\n        (\"metadata.task.sync_space.sync_bcs_space\", \"*/10 * * * *\"),\n        (\"metadata.task.sync_space.refresh_bcs_project_biz\", \"*/10 * * * *\"),\n        # metadata同步自定义时序维度信息, 每5分钟将会从consul同步一次\n        (\"metadata.task.custom_report.check_update_ts_metric\", \"*/5 * * * *\"),\n        # 每天4点半执行一次\n        (\"metadata.task.vm.refresh_query_vm_space_list\", \"30 4 * * *\"),\n    ]\n    # 耗时任务单独队列处理\n    LONG_TASK_CRONTAB = [\n        # 清理任务耗时较久，半个小时执行一次\n        (\"metadata.task.config_refresh.clean_influxdb_tag\", \"*/30 * * * *\"),\n        (\"metadata.task.config_refresh.clean_influxdb_storage\", \"*/30 * * * *\"),\n        (\"metadata.task.config_refresh.clean_influxdb_cluster\", \"*/30 * * * *\"),\n        (\"metadata.task.config_refresh.clean_influxdb_host\", \"*/30 * * * *\"),\n        # 刷新数据源信息到 consul\n        (\"metadata.task.config_refresh.refresh_datasource\", \"*/10 * * * *\"),\n        # 刷新 storage 信息给unify-query使用\n        # TODO: 待确认是否还有使用，如没使用可以删除\n        (\"metadata.task.config_refresh.refresh_consul_influxdb_tableinfo\", \"*/10 * * * *\"),\n        # 刷新结果表路由配置\n        (\"metadata.task.config_refresh.refresh_influxdb_route\", \"*/10 * * * *\"),\n        # 刷新空间信息，业务、BCS的关联资源\n        (\"metadata.task.sync_space.refresh_cluster_resource\", \"*/30 * * * *\"),\n        (\"metadata.task.sync_space.refresh_redis_data\", \"*/30 * * * *\"),\n        (\"metadata.task.sync_space.sync_bkcc_space_data_source\", \"*/10 * * * *\"),\n        (\"metadata.task.sync_space.refresh_not_biz_space_data_source\", \"*/10 * * * *\"),\n        # metadata 同步自定义事件维度及事件，每三分钟将会从ES同步一次\n        (\"metadata.task.custom_report.check_event_update\", \"*/3 * * * *\"),\n        # metadata 同步 bkci 空间名称任务，因为不要求实时性，每天3点执行一次\n        (\"metadata.task.sync_space.refresh_bkci_space_name\", \"0 3 * * *\"),\n        # metadata 更新 bkcc 空间名称任务，因为不要求实时性，每天3点半执行一次\n        (\"metadata.task.sync_space.refresh_bkcc_space_name\", \"30 3 * * *\"),\n        # metadata 刷新 unify_query 视图需要的字段，因为变动性很低，每天 4 点执行一次\n        (\"metadata.task.config_refresh.refresh_unify_query_additional_config\", \"0 4 * * *\"),\n    ]\n\n# Timeout for image exporter service, default set to 10 seconds\nIMAGE_EXPORTER_TIMEOUT = 10\n\nAES_X_KEY_FIELD = \"SAAS_SECRET_KEY\"\n\n# gse alarm dataid\nGSE_BASE_ALARM_DATAID = 1000\nGSE_CUSTOM_EVENT_DATAID = 1100000\n\nBKMONITOR_WORKER_INET_DEV = \"\"\nBKMONITOR_WORKER_INCLUDE_LIST = []\nBKMONITOR_WORKER_EXCLUDE_LIST = []\n\n# ACTION\nMESSAGE_QUEUE_MAX_LENGTH = 0\n\n# SELF-MONITOR\nSUPERVISOR_PROCESS_UPTIME = 10\n\nSELFMONITOR_PORTS = {\"gse-data\": 58625}\n\n# 计算平台localTime与UTC时间的差值\nBKDATA_LOCAL_TIMEZONE_OFFSET = -8\n# 计算平台数据的localTime与当前时间比较的阈值，小于该值时下次再拉取数据\nBKDATA_LOCAL_TIME_THRESHOLD = 10\n\n# 跳过权限中心检查\nSKIP_IAM_PERMISSION_CHECK = True\n\n# event 模块最大容忍无数据周期数\nEVENT_NO_DATA_TOLERANCE_WINDOW_SIZE = 5\n\nANOMALY_RECORD_COLLECT_WINDOW = 100\nANOMALY_RECORD_CONVERGED_ACTION_WINDOW = 3\n\n# access模块策略拉取耗时限制（每10分钟）\nACCESS_TIME_PER_WINDOW = 30\n\n# 环境变量\nPYTHON_HOME = sys.executable.rsplit(\"/\", 1)[0]  # virtualenv path\nPYTHON = PYTHON_HOME + \"/python\"  # python bin\nGUNICORN = PYTHON_HOME + \"/gunicorn\"  # gunicorn bin\n\nLOG_LOGFILE_MAXSIZE = 1024 * 1024 * 200  # 200m\nLOG_LOGFILE_BACKUP_COUNT = 12\nLOG_PROCESS_CHECK_TIME = 60 * 60 * 4\nLOG_LOGFILE_BACKUP_GZIP = True\n\n# LOGGING\nLOGGER_LEVEL = os.environ.get(\"BKAPP_LOG_LEVEL\", \"INFO\")\nLOG_FILE_PATH = os.path.join(LOG_PATH, f\"{LOG_FILE_PREFIX}kernel.log\")\nLOG_IMAGE_EXPORTER_FILE_PATH = os.path.join(LOG_PATH, f\"{LOG_FILE_PREFIX}kernel_image_exporter.log\")\nLOG_METADATA_FILE_PATH = os.path.join(LOG_PATH, f\"{LOG_FILE_PREFIX}kernel_metadata.log\")\nLOGGER_DEFAULT = {\"level\": LOGGER_LEVEL, \"handlers\": [\"console\", \"file\"]}\n\n\ndef get_logger_config(log_path, logger_level, log_file_prefix):\n    return {\n        \"version\": 1,\n        \"loggers\": {\n            \"root\": LOGGER_DEFAULT,\n            \"core\": LOGGER_DEFAULT,\n            \"cron\": LOGGER_DEFAULT,\n            \"cache\": LOGGER_DEFAULT,\n            \"service\": LOGGER_DEFAULT,\n            \"detect\": LOGGER_DEFAULT,\n            \"nodata\": LOGGER_DEFAULT,\n            \"access\": LOGGER_DEFAULT,\n            \"trigger\": LOGGER_DEFAULT,\n            \"event\": LOGGER_DEFAULT,\n            \"alert\": LOGGER_DEFAULT,\n            \"composite\": LOGGER_DEFAULT,\n            \"recovery\": LOGGER_DEFAULT,\n            \"fta_action\": LOGGER_DEFAULT,\n            \"bkmonitor\": LOGGER_DEFAULT,\n            \"apm\": LOGGER_DEFAULT,\n            \"data_source\": LOGGER_DEFAULT,\n            \"alarm_backends\": LOGGER_DEFAULT,\n            \"self_monitor\": LOGGER_DEFAULT,\n            \"calendars\": LOGGER_DEFAULT,\n            \"celery\": LOGGER_DEFAULT,\n            \"kubernetes\": LOGGER_DEFAULT,\n            \"metadata\": {\"level\": LOGGER_LEVEL, \"propagate\": False, \"handlers\": [\"metadata\"]},\n            \"image_exporter\": {\"level\": LOGGER_LEVEL, \"propagate\": False, \"handlers\": [\"image_exporter\"]},\n        },\n        \"handlers\": {\n            \"console\": {\"class\": \"logging.StreamHandler\", \"level\": \"DEBUG\", \"formatter\": \"standard\"},\n            \"file\": {\n                \"class\": \"logging.handlers.WatchedFileHandler\",\n                \"level\": \"DEBUG\",\n                \"formatter\": \"standard\",\n                \"filename\": os.path.join(log_path, f\"{log_file_prefix}kernel.log\"),\n                \"encoding\": \"utf-8\",\n            },\n            \"image_exporter\": {\n                \"class\": \"logging.handlers.WatchedFileHandler\",\n                \"level\": \"DEBUG\",\n                \"formatter\": \"standard\",\n                \"filename\": os.path.join(log_path, f\"{log_file_prefix}kernel_image_exporter.log\"),\n                \"encoding\": \"utf-8\",\n            },\n            \"metadata\": {\n                \"class\": \"logging.handlers.WatchedFileHandler\",\n                \"level\": \"DEBUG\",\n                \"formatter\": \"standard\",\n                \"filename\": os.path.join(log_path, f\"{log_file_prefix}kernel_metadata.log\"),\n                \"encoding\": \"utf-8\",\n            },\n        },\n        \"formatters\": {\n            \"standard\": {\n                \"format\": (\n                    \"%(asctime)s %(levelname)-8s %(process)-8d\" \"%(name)-15s %(filename)20s[%(lineno)03d] %(message)s\"\n                ),\n                \"datefmt\": \"%Y-%m-%d %H:%M:%S\",\n            }\n        },\n    }\n\n\nLOGGING = LOGGER_CONF = get_logger_config(LOG_PATH, LOGGER_LEVEL, LOG_FILE_PREFIX)\n\nif IS_CONTAINER_MODE:\n    for logger in LOGGING[\"loggers\"]:\n        if \"null\" not in LOGGING[\"loggers\"][logger][\"handlers\"]:\n            LOGGING[\"loggers\"][logger][\"handlers\"] = [\"console\"]\n\n# Consul\n(\n    CONSUL_CLIENT_HOST,\n    CONSUL_CLIENT_PORT,\n    CONSUL_HTTPS_PORT,\n    CONSUL_CLIENT_CERT_FILE,\n    CONSUL_CLIENT_KEY_FILE,\n    CONSUL_SERVER_CA_CERT,\n    CONSUL_TOKEN,\n) = get_consul_settings()\n\n# Redis\nCACHE_BACKEND_TYPE, REDIS_HOST, REDIS_PORT, REDIS_PASSWD, REDIS_MASTER_NAME, REDIS_SENTINEL_PASS = get_redis_settings()\n\n# redis中的db分配[7，8，9，10]，共4个db\n# 7.[不重要，可清理] 日志相关数据使用log配置\n# 8.[一般，可清理]   配置相关缓存使用cache配置，例如：cmdb的数据、策略、屏蔽等配置数据\n# 9.[重要，不可清理] 各个services之间交互的队列，使用queue配置\n# 9.[重要，不可清理] celery的broker，使用celery配置\n# 10.[重要，不可清理] service自身的数据，使用service配置\nREDIS_LOG_CONF = {\"host\": REDIS_HOST, \"port\": REDIS_PORT, \"db\": 7, \"password\": REDIS_PASSWD}\nREDIS_CACHE_CONF = {\"host\": REDIS_HOST, \"port\": REDIS_PORT, \"db\": 8, \"password\": REDIS_PASSWD}\nREDIS_CELERY_CONF = REDIS_QUEUE_CONF = {\"host\": REDIS_HOST, \"port\": REDIS_PORT, \"db\": 9, \"password\": REDIS_PASSWD}\nREDIS_SERVICE_CONF = {\"host\": REDIS_HOST, \"port\": REDIS_PORT, \"db\": 10, \"password\": REDIS_PASSWD, \"socket_timeout\": 10}\n\n# TRANSFER\nTRANSFER_HOST = os.environ.get(\"BK_TRANSFER_HOST\", \"transfer.bkmonitorv3.service.consul\")\nTRANSFER_PORT = os.environ.get(\"BK_TRANSFER_HTTP_PORT\", 10202)\n\n# INFLUXDB\nINFLUXDB_HOST = os.environ.get(\"BK_INFLUXDB_PROXY_HOST\", \"influxdb-proxy.bkmonitorv3.service.consul\")\nINFLUXDB_PORT = int(os.environ.get(\"BK_INFLUXDB_PROXY_PORT\", 10203))\n\n# zookeeper\nZK_HOST = os.environ.get(\"BK_GSE_ZK_HOST\", \"zk.service.consul\")\nZK_PORT = int(os.environ.get(\"BK_GSE_ZK_PORT\", 2181))\n\nCERT_PATH = os.environ.get(\"BK_CERT_PATH\", \"\")\nLICENSE_HOST = os.environ.get(\"BK_LICENSE_HOST\", \"license.service.consul\")\nLICENSE_PORT = os.environ.get(\"BK_LICENSE_PORT\", \"8443\")\nLICENSE_REQ_INTERVAL = [20, 60, 120]  # 连续请求n次，每次请求间隔(单位：秒)\n\nRABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_VHOST, RABBITMQ_USER, RABBITMQ_PASS, _ = get_rabbitmq_settings(\n    app_code=APP_CODE, backend=True\n)\n\n# esb组件地址\nCOMMON_USERNAME = os.environ.get(\"BK_ESB_SUPER_USER\", \"admin\")\n\nCACHES = {\n    \"default\": {\n        \"BACKEND\": \"django.core.cache.backends.db.DatabaseCache\",\n        \"LOCATION\": \"django_cache\",\n        \"OPTIONS\": {\"MAX_ENTRIES\": 100000, \"CULL_FREQUENCY\": 10},\n    },\n    \"db\": {\n        \"BACKEND\": \"django.core.cache.backends.db.DatabaseCache\",\n        \"LOCATION\": \"django_cache\",\n        \"OPTIONS\": {\"MAX_ENTRIES\": 100000, \"CULL_FREQUENCY\": 10},\n    },\n    \"login_db\": {\"BACKEND\": \"django.core.cache.backends.db.DatabaseCache\", \"LOCATION\": \"account_cache\"},\n    \"locmem\": {\"BACKEND\": \"django.core.cache.backends.locmem.LocMemCache\"},\n}\n\n# django cache backend using redis\nDJANGO_REDIS_PASSWORD = crypto_util.sm4_decrypt(os.environ.get(\"DJANGO_REDIS_PASSWORD\"))\nDJANGO_REDIS_HOST = os.environ.get(\"DJANGO_REDIS_HOST\")\nDJANGO_REDIS_PORT = os.environ.get(\"DJANGO_REDIS_PORT\")\nDJANGO_REDIS_DB = os.environ.get(\"DJANGO_REDIS_DB\")\nUSE_DJANGO_CACHE_REDIS = \"DJANGO_REDIS_HOST\" in os.environ and \"DJANGO_REDIS_PORT\" in os.environ\nif USE_DJANGO_CACHE_REDIS:\n    CACHES[\"redis\"] = {\n        \"BACKEND\": \"django_redis.cache.RedisCache\",\n        \"LOCATION\": \"redis://:{}@{}:{}/{}\".format(\n            DJANGO_REDIS_PASSWORD or \"\", DJANGO_REDIS_HOST, DJANGO_REDIS_PORT, DJANGO_REDIS_DB or \"0\"\n        ),\n        \"OPTIONS\": {\n            \"CLIENT_CLASS\": \"django_redis.client.DefaultClient\",\n            \"COMPRESSOR\": \"django_redis.compressors.zlib.ZlibCompressor\",\n        },\n    }\n    CACHES[\"default\"] = CACHES[\"redis\"]\n\n# 全局告警屏蔽开关\nGLOBAL_SHIELD_ENABLED = False\n\n# 处理动作丢弃阈值\nQOS_DROP_ACTION_THRESHOLD = 100\n# 处理动作流控窗口大小\nQOS_DROP_ACTION_WINDOW = 60\n\n# 处理动作丢弃阈值\nQOS_ALERT_THRESHOLD = 200\n# 处理动作流控窗口大小\nQOS_ALERT_WINDOW = 60\n\n# 第三方事件接入白名单\nBIZ_WHITE_LIST_FOR_3RD_EVENT = []\n\n# 自定义指标过期时间\nTIME_SERIES_METRIC_EXPIRED_DAYS = 30\n\n# 自定义指标拉取最大步长\nMAX_METRICS_FETCH_STEP = os.environ.get(\"MAX_METRICS_FETCH_STEP\", 500)\nMETRICS_KEY_PREFIX = \"bkmonitor:metrics_\"\nMETRIC_DIMENSIONS_KEY_PREFIX = \"bkmonitor:metric_dimensions_\"\n\n# 默认 Kafka 存储集群 ID\nDEFAULT_KAFKA_STORAGE_CLUSTER_ID = None\n# BCS Kafka 存储集群 ID\nBCS_KAFKA_STORAGE_CLUSTER_ID = None\n# 自定义上报时间存储集群\nBCS_CUSTOM_EVENT_STORAGE_CLUSTER_ID = None\n\n# 无数据告警过期时间\nNO_DATA_ALERT_EXPIRED_TIMEDELTA = 24 * 60 * 60\n\n# 计算平台计算 FLOW 存储数据的 HDFS 的集群\nBKDATA_FLOW_HDFS_CLUSTER = os.environ.get(\"BKDATA_FLOW_HDFS_CLUSTER\", \"hdfsOnline4\")\n\n# 单次build告警的event数量设置\nMAX_BUILD_EVENT_NUMBER = 0\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/bkmonitor/config/role/worker.py b/bkmonitor/config/role/worker.py
--- a/bkmonitor/config/role/worker.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/bkmonitor/config/role/worker.py	(date 1745303880103)
@@ -117,53 +117,53 @@
     # Notice Notice Notice:
     # Use UTC's time zone to set your crontab instead of the local time zone
     # cmdb cache
-    ("alarm_backends.core.cache.cmdb.host", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.module", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.set", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.business", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.service_instance", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.topo", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.service_template", "*/30 * * * *"),
-    ("alarm_backends.core.cache.cmdb.set_template", "*/30 * * * *"),
-    # model cache
-    ("alarm_backends.core.cache.strategy", "* * * * *"),
-    ("alarm_backends.core.cache.shield", "* * * * *"),
-    ("alarm_backends.core.cache.models.collect_config", "* * * * *"),
-    ("alarm_backends.core.cache.models.uptimecheck", "* * * * *"),
-    ("alarm_backends.core.cache.action_config", "* * * * *"),
-    ("alarm_backends.core.cache.assign", "* * * * *"),
-    ("alarm_backends.core.cache.calendar", "* * * * *"),
-    # api cache
-    ("alarm_backends.core.cache.result_table", "*/10 * * * *"),
-    # hash ring result
-    ("alarm_backends.core.cache.hash_ring", "* * * * *"),
-    # delay queue
-    ("alarm_backends.core.cache.delay_queue", "* * * * *"),
-    # bcs cluster cache
-    ("alarm_backends.core.cache.bcs_cluster", "*/10 * * * *"),
-    # clean detect result cache
-    ("alarm_backends.core.detect_result.tasks.clean_expired_detect_result", "0 */2 * * *"),
-    ("alarm_backends.core.detect_result.tasks.clean_md5_to_dimension_cache", "0 23 * * *"),
-    # 定期清理超时未执行任务
-    ("alarm_backends.service.fta_action.tasks.check_timeout_actions", "* * * * *"),
-    # 定期清理mysql内半个月前的数据
-    ("alarm_backends.service.fta_action.tasks.clear_mysql_action_data", "* * * * *"),
-    ("bkmonitor.documents.tasks.rollover_indices", "*/10 * * * *"),
-    # mail_report 配置管理和告警接收人信息缓存
-    ("alarm_backends.core.cache.mail_report", "*/30 * * * *"),
-    # apm topo discover: 每分钟触发，每次分片处理1/10应用
-    ("apm.task.tasks.topo_discover_cron", "* * * * *"),
-    # apm 配置下发: 每分钟触发，每次分片处理1/30应用
-    ("apm.task.tasks.refresh_apm_config", "* * * * *"),
-    ("apm.task.tasks.refresh_apm_platform_config", "*/30 * * * *"),
-    # apm 检测预计算表字段是否有更新 1小时执行检测一次
-    ("apm.task.tasks.check_pre_calculate_fields_update", "0 */1 * * *"),
+    # ("alarm_backends.core.cache.cmdb.host", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.module", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.set", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.business", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.service_instance", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.topo", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.service_template", "*/30 * * * *"),
+    # ("alarm_backends.core.cache.cmdb.set_template", "*/30 * * * *"),
+    # # model cache
+    # ("alarm_backends.core.cache.strategy", "* * * * *"),
+    # ("alarm_backends.core.cache.shield", "* * * * *"),
+    # ("alarm_backends.core.cache.models.collect_config", "* * * * *"),
+    # ("alarm_backends.core.cache.models.uptimecheck", "* * * * *"),
+    # ("alarm_backends.core.cache.action_config", "* * * * *"),
+    # ("alarm_backends.core.cache.assign", "* * * * *"),
+    # ("alarm_backends.core.cache.calendar", "* * * * *"),
+    # # api cache
+    # ("alarm_backends.core.cache.result_table", "*/10 * * * *"),
+    # # hash ring result
+    # ("alarm_backends.core.cache.hash_ring", "* * * * *"),
+    # # delay queue
+    # ("alarm_backends.core.cache.delay_queue", "* * * * *"),
+    # # bcs cluster cache
+    # ("alarm_backends.core.cache.bcs_cluster", "*/10 * * * *"),
+    # # clean detect result cache
+    # ("alarm_backends.core.detect_result.tasks.clean_expired_detect_result", "0 */2 * * *"),
+    # ("alarm_backends.core.detect_result.tasks.clean_md5_to_dimension_cache", "0 23 * * *"),
+    # # 定期清理超时未执行任务
+    # ("alarm_backends.service.fta_action.tasks.check_timeout_actions", "* * * * *"),
+    # # 定期清理mysql内半个月前的数据
+    # ("alarm_backends.service.fta_action.tasks.clear_mysql_action_data", "* * * * *"),
+    # ("bkmonitor.documents.tasks.rollover_indices", "*/10 * * * *"),
+    # # mail_report 配置管理和告警接收人信息缓存
+    # ("alarm_backends.core.cache.mail_report", "*/30 * * * *"),
+    # # apm topo discover: 每分钟触发，每次分片处理1/10应用
+    # ("apm.task.tasks.topo_discover_cron", "* * * * *"),
+    # # apm 配置下发: 每分钟触发，每次分片处理1/30应用
+    # ("apm.task.tasks.refresh_apm_config", "* * * * *"),
+    # ("apm.task.tasks.refresh_apm_platform_config", "*/30 * * * *"),
+    # # apm 检测预计算表字段是否有更新 1小时执行检测一次
+    # ("apm.task.tasks.check_pre_calculate_fields_update", "0 */1 * * *"),
 ]
 
 if BCS_API_GATEWAY_HOST:
     DEFAULT_CRONTAB += [
         # bcs资源同步
-        ("api.bcs.tasks.sync_bcs_cluster_to_db", "*/10 * * * *"),
+        ("api.bcs.tasks.sync_bcs_cluster_to_db", "*/2 * * * *"),
         ("api.bcs.tasks.sync_bcs_service_to_db", "*/10 * * * *"),
         ("api.bcs.tasks.sync_bcs_workload_to_db", "*/10 * * * *"),
         ("api.bcs.tasks.sync_bcs_pod_to_db", "*/10 * * * *"),
@@ -184,83 +184,83 @@
 
 ACTION_TASK_CRONTAB = [
     # 定期检测异常告警
-    ("alarm_backends.service.alert.manager.tasks.check_abnormal_alert", "* * * * *"),
-    # 定期关闭流控告警，避免与整点之类的任务并发，设置每12分钟执行一次
-    ("alarm_backends.service.alert.manager.tasks.check_blocked_alert", "*/12 * * * *"),
-    # 定期检测屏蔽策略，进行告警的屏蔽
-    ("alarm_backends.service.converge.shield.tasks.check_and_send_shield_notice", "* * * * *"),
-    # 定期同步数据至es
-    ("alarm_backends.service.fta_action.tasks.sync_action_instances", "* * * * *"),
-    # 定期维护排班计划
-    ("alarm_backends.service.fta_action.tasks.generate_duty_plan_task", "* * * * *"),
-    # 定期巡检周期通知和回调任务
-    ("alarm_backends.service.fta_action.tasks.check_create_poll_action", "* * * * *"),
-    # 定期处理demo任务
-    ("alarm_backends.service.fta_action.tasks.dispatch_demo_action_tasks", "* * * * *"),
+    # ("alarm_backends.service.alert.manager.tasks.check_abnormal_alert", "* * * * *"),
+    # # 定期关闭流控告警，避免与整点之类的任务并发，设置每12分钟执行一次
+    # ("alarm_backends.service.alert.manager.tasks.check_blocked_alert", "*/12 * * * *"),
+    # # 定期检测屏蔽策略，进行告警的屏蔽
+    # ("alarm_backends.service.converge.shield.tasks.check_and_send_shield_notice", "* * * * *"),
+    # # 定期同步数据至es
+    # ("alarm_backends.service.fta_action.tasks.sync_action_instances", "* * * * *"),
+    # # 定期维护排班计划
+    # ("alarm_backends.service.fta_action.tasks.generate_duty_plan_task", "* * * * *"),
+    # # 定期巡检周期通知和回调任务
+    # ("alarm_backends.service.fta_action.tasks.check_create_poll_action", "* * * * *"),
+    # # 定期处理demo任务
+    # ("alarm_backends.service.fta_action.tasks.dispatch_demo_action_tasks", "* * * * *"),
 ]
 
 if os.getenv("DISABLE_METADATA_TASK") != "True":
     DEFAULT_CRONTAB += [
         # metadata
         # metadata更新每个influxdb的存储RP，UTC时间的22点进行更新，待0点influxdb进行清理
-        ("metadata.task.refresh_default_rp", "0 22 * * *"),
-        # metadata同步pingserver配置，下发iplist到proxy机器，每10分钟执行一次
-        ("metadata.task.ping_server.refresh_ping_server_2_node_man", "*/10 * * * *"),
-        # metadata同步自定义上报配置到节点管理，完成配置订阅，理论上，在配置变更的时候，会执行一次，所以这里运行周期可以放大
-        ("metadata.task.custom_report.refresh_custom_report_2_node_man", "*/5 * * * *"),
-        # metadata自动部署bkmonitorproxy
-        ("metadata.task.auto_deploy_proxy", "30 */2 * * *"),
-        ("metadata.task.config_refresh.refresh_kafka_storage", "*/10 * * * *"),
-        ("metadata.task.config_refresh.refresh_kafka_topic_info", "*/10 * * * *"),
-        ("metadata.task.config_refresh.refresh_consul_es_info", "*/10 * * * *"),
-        ("metadata.task.config_refresh.refresh_consul_storage", "*/10 * * * *"),
-        ("metadata.task.config_refresh.refresh_bcs_info", "*/10 * * * *"),
-        # 刷新metadata降精度配置，10分钟一次
-        ("metadata.task.downsampled.refresh_influxdb_downsampled", "*/10 * * * *"),
-        # 降精度计算，5分钟一次检测一次
-        ("metadata.task.downsampled.access_and_calc_for_downsample", "*/5 * * * *"),
-        # 刷新回溯配置
-        ("metadata.task.config_refresh.refresh_es_restore", "* * * * *"),
-        # bcs信息刷新
-        ("metadata.task.bcs.refresh_bcs_monitor_info", "*/10 * * * *"),
-        ("metadata.task.bcs.refresh_bcs_metrics_label", "*/10 * * * *"),
-        ("metadata.task.bcs.discover_bcs_clusters", "*/10 * * * *"),
-        # 同步空间信息
-        ("metadata.task.sync_space.sync_bkcc_space", "*/10 * * * *"),
-        ("metadata.task.sync_space.sync_bcs_space", "*/10 * * * *"),
-        ("metadata.task.sync_space.refresh_bcs_project_biz", "*/10 * * * *"),
-        # metadata同步自定义时序维度信息, 每5分钟将会从consul同步一次
-        ("metadata.task.custom_report.check_update_ts_metric", "*/5 * * * *"),
-        # 每天4点半执行一次
-        ("metadata.task.vm.refresh_query_vm_space_list", "30 4 * * *"),
+        # ("metadata.task.refresh_default_rp", "0 22 * * *"),
+        # # metadata同步pingserver配置，下发iplist到proxy机器，每10分钟执行一次
+        # ("metadata.task.ping_server.refresh_ping_server_2_node_man", "*/10 * * * *"),
+        # # metadata同步自定义上报配置到节点管理，完成配置订阅，理论上，在配置变更的时候，会执行一次，所以这里运行周期可以放大
+        # ("metadata.task.custom_report.refresh_custom_report_2_node_man", "*/5 * * * *"),
+        # # metadata自动部署bkmonitorproxy
+        # ("metadata.task.auto_deploy_proxy", "30 */2 * * *"),
+        # ("metadata.task.config_refresh.refresh_kafka_storage", "*/10 * * * *"),
+        # ("metadata.task.config_refresh.refresh_kafka_topic_info", "*/10 * * * *"),
+        # ("metadata.task.config_refresh.refresh_consul_es_info", "*/10 * * * *"),
+        # ("metadata.task.config_refresh.refresh_consul_storage", "*/10 * * * *"),
+        # ("metadata.task.config_refresh.refresh_bcs_info", "*/10 * * * *"),
+        # # 刷新metadata降精度配置，10分钟一次
+        # ("metadata.task.downsampled.refresh_influxdb_downsampled", "*/10 * * * *"),
+        # # 降精度计算，5分钟一次检测一次
+        # ("metadata.task.downsampled.access_and_calc_for_downsample", "*/5 * * * *"),
+        # # 刷新回溯配置
+        # ("metadata.task.config_refresh.refresh_es_restore", "* * * * *"),
+        # # bcs信息刷新
+        # ("metadata.task.bcs.refresh_bcs_monitor_info", "*/10 * * * *"),
+        # ("metadata.task.bcs.refresh_bcs_metrics_label", "*/10 * * * *"),
+        # ("metadata.task.bcs.discover_bcs_clusters", "*/10 * * * *"),
+        # # 同步空间信息
+        # ("metadata.task.sync_space.sync_bkcc_space", "*/10 * * * *"),
+        # ("metadata.task.sync_space.sync_bcs_space", "*/10 * * * *"),
+        # ("metadata.task.sync_space.refresh_bcs_project_biz", "*/10 * * * *"),
+        # # metadata同步自定义时序维度信息, 每5分钟将会从consul同步一次
+        # ("metadata.task.custom_report.check_update_ts_metric", "*/5 * * * *"),
+        # # 每天4点半执行一次
+        # ("metadata.task.vm.refresh_query_vm_space_list", "30 4 * * *"),
     ]
     # 耗时任务单独队列处理
     LONG_TASK_CRONTAB = [
-        # 清理任务耗时较久，半个小时执行一次
-        ("metadata.task.config_refresh.clean_influxdb_tag", "*/30 * * * *"),
-        ("metadata.task.config_refresh.clean_influxdb_storage", "*/30 * * * *"),
-        ("metadata.task.config_refresh.clean_influxdb_cluster", "*/30 * * * *"),
-        ("metadata.task.config_refresh.clean_influxdb_host", "*/30 * * * *"),
-        # 刷新数据源信息到 consul
-        ("metadata.task.config_refresh.refresh_datasource", "*/10 * * * *"),
-        # 刷新 storage 信息给unify-query使用
-        # TODO: 待确认是否还有使用，如没使用可以删除
-        ("metadata.task.config_refresh.refresh_consul_influxdb_tableinfo", "*/10 * * * *"),
-        # 刷新结果表路由配置
-        ("metadata.task.config_refresh.refresh_influxdb_route", "*/10 * * * *"),
-        # 刷新空间信息，业务、BCS的关联资源
-        ("metadata.task.sync_space.refresh_cluster_resource", "*/30 * * * *"),
-        ("metadata.task.sync_space.refresh_redis_data", "*/30 * * * *"),
-        ("metadata.task.sync_space.sync_bkcc_space_data_source", "*/10 * * * *"),
-        ("metadata.task.sync_space.refresh_not_biz_space_data_source", "*/10 * * * *"),
-        # metadata 同步自定义事件维度及事件，每三分钟将会从ES同步一次
-        ("metadata.task.custom_report.check_event_update", "*/3 * * * *"),
-        # metadata 同步 bkci 空间名称任务，因为不要求实时性，每天3点执行一次
-        ("metadata.task.sync_space.refresh_bkci_space_name", "0 3 * * *"),
-        # metadata 更新 bkcc 空间名称任务，因为不要求实时性，每天3点半执行一次
-        ("metadata.task.sync_space.refresh_bkcc_space_name", "30 3 * * *"),
-        # metadata 刷新 unify_query 视图需要的字段，因为变动性很低，每天 4 点执行一次
-        ("metadata.task.config_refresh.refresh_unify_query_additional_config", "0 4 * * *"),
+        # # 清理任务耗时较久，半个小时执行一次
+        # ("metadata.task.config_refresh.clean_influxdb_tag", "*/30 * * * *"),
+        # ("metadata.task.config_refresh.clean_influxdb_storage", "*/30 * * * *"),
+        # ("metadata.task.config_refresh.clean_influxdb_cluster", "*/30 * * * *"),
+        # ("metadata.task.config_refresh.clean_influxdb_host", "*/30 * * * *"),
+        # # 刷新数据源信息到 consul
+        # ("metadata.task.config_refresh.refresh_datasource", "*/10 * * * *"),
+        # # 刷新 storage 信息给unify-query使用
+        # # TODO: 待确认是否还有使用，如没使用可以删除
+        # ("metadata.task.config_refresh.refresh_consul_influxdb_tableinfo", "*/10 * * * *"),
+        # # 刷新结果表路由配置
+        # ("metadata.task.config_refresh.refresh_influxdb_route", "*/10 * * * *"),
+        # # 刷新空间信息，业务、BCS的关联资源
+        # ("metadata.task.sync_space.refresh_cluster_resource", "*/30 * * * *"),
+        # ("metadata.task.sync_space.refresh_redis_data", "*/30 * * * *"),
+        # ("metadata.task.sync_space.sync_bkcc_space_data_source", "*/10 * * * *"),
+        # ("metadata.task.sync_space.refresh_not_biz_space_data_source", "*/10 * * * *"),
+        # # metadata 同步自定义事件维度及事件，每三分钟将会从ES同步一次
+        # ("metadata.task.custom_report.check_event_update", "*/3 * * * *"),
+        # # metadata 同步 bkci 空间名称任务，因为不要求实时性，每天3点执行一次
+        # ("metadata.task.sync_space.refresh_bkci_space_name", "0 3 * * *"),
+        # # metadata 更新 bkcc 空间名称任务，因为不要求实时性，每天3点半执行一次
+        # ("metadata.task.sync_space.refresh_bkcc_space_name", "30 3 * * *"),
+        # # metadata 刷新 unify_query 视图需要的字段，因为变动性很低，每天 4 点执行一次
+        # ("metadata.task.config_refresh.refresh_unify_query_additional_config", "0 4 * * *"),
     ]
 
 # Timeout for image exporter service, default set to 10 seconds
@@ -512,3 +512,8 @@
 
 # 单次build告警的event数量设置
 MAX_BUILD_EVENT_NUMBER = 0
+
+logger = logging.getLogger(__name__)
+print(__name__)
+print("-------------------------------------------------------------------------------ttttt")
+logger.info("-------------------------------------------------------------------------------log")
Index: bkmonitor/alarm_backends/core/api_cache/library.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># -*- coding: utf-8 -*-\n\"\"\"\nTencent is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.\nCopyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.\nLicensed under the MIT License (the \"License\"); you may not use this file except in compliance with the License.\nYou may obtain a copy of the License at http://opensource.org/licenses/MIT\nUnless required by applicable law or agreed to in writing, software distributed under the License is distributed on\nan \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the\nspecific language governing permissions and limitations under the License.\n\"\"\"\n\n\"\"\"\ncelery api任务\n\"\"\"\nimport datetime\nimport gc\nimport logging\nimport os\nimport socket\nimport time\n\nimport psutil\nfrom django.conf import settings\nfrom django.core.cache import caches\nfrom django.db import close_old_connections\nfrom gevent.monkey import saved\nfrom six.moves import range\n\nfrom alarm_backends.constants import CONST_MINUTES, CONST_ONE_HOUR\nfrom alarm_backends.core.lock.service_lock import share_lock\nfrom alarm_backends.management.hashring import HashRing\nfrom alarm_backends.service.scheduler.app import app\nfrom api.cmdb import client\nfrom api.cmdb.define import Host\nfrom bkmonitor.models import StrategyModel\nfrom bkmonitor.utils.common_utils import get_local_ip\nfrom bkmonitor.utils.supervisor_utils import get_supervisor_client\nfrom core.drf_resource import api\n\nlogger = logging.getLogger(__name__)\n\nMAX_CONCURRENCE_NUMBER = 20\ngevent_active = \"time\" in saved\n\nIP = get_local_ip()\n\n\ndef active_biz_ids():\n    biz_ids = StrategyModel.objects.all().values(\"bk_biz_id\").distinct()\n    biz_count = len(biz_ids)\n    if biz_count > 100:\n        global HR, RING_NODES\n        RING_NODES *= len(str((biz_count + 1) // 10))\n        HR = HashRing(dict.fromkeys(list(range(RING_NODES)), 1))\n        logger.info(f\"[api cache] hash ring nodes: {RING_NODES}\")\n    return biz_ids\n\n\n@share_lock()\ndef cache_business():\n    \"\"\"\n        CC_CACHE_ALWAYS                 None\n    api.cmdb.search_cloud_area -> client.search_cloud_area\n    client.search_business\n    :return:\n    \"\"\"\n    client.search_business.request.refresh()\n    api.cmdb.search_cloud_area.request.refresh()\n\n\ndef func_adapter_resource(func):\n    setattr(func, \"request\", func)\n\n\ndef cmdb_api_list(bk_biz_id):\n    \"\"\"\n    这里周期刷新的接口，可以设置cache_type = CacheType.CC_CACHE_ALWAYS\n    client.xxx: 底层api调用，直接设置CC_CACHE_ALWAYS\n        client.search_biz_inst_topo\n        client.get_biz_internal_module\n        client.list_service_category\n    api.cmdb.xxx: 上层封装，因此具体实现逻辑中，调用的client.xxx 不需要设置CacheType\n        CC_CACHE_ALWAYS                         None\n        api.cmdb.get_host_dict_by_biz -> client.list_biz_hosts_topo\n        api.cmdb.get_service_instance_by_biz -> client.list_service_instance_detail\n    \"\"\"\n    # api模块下非resource对象(仅函数) 没有request 方法，需要设置一下\n    func_adapter_resource(api.cmdb.get_host_dict_by_biz)\n    func_adapter_resource(api.cmdb.get_service_instance_by_biz)\n    cmdb_tasks = [\n        {\"api\": client.search_biz_inst_topo, \"args\": (), \"kwargs\": {\"bk_biz_id\": bk_biz_id}},\n        {\n            \"api\": client.get_biz_internal_module,\n            \"args\": (),\n            \"kwargs\": {\"bk_biz_id\": bk_biz_id, \"bk_supplier_account\": settings.BK_SUPPLIER_ACCOUNT},\n        },\n        {\"api\": api.cmdb.get_service_instance_by_biz, \"args\": (bk_biz_id,), \"kwargs\": {}},\n        {\"api\": client.list_service_category, \"args\": (), \"kwargs\": {\"bk_biz_id\": bk_biz_id}},\n        {\"api\": api.cmdb.get_host_dict_by_biz, \"args\": (bk_biz_id, Host.Fields), \"kwargs\": {}},\n    ]\n    return cmdb_tasks\n\n\n@share_lock(identify=IP)\ndef cache_cmdb_resource():\n    \"\"\"\n    只有配置了采集任务，这个接口就会反复被调用，但是表在SaaS的库，这边全部缓存\n    \"\"\"\n    from gevent.pool import Pool\n\n    api_coroutines = []\n    biz_result = active_biz_ids()\n    pool = Pool(MAX_CONCURRENCE_NUMBER)\n    minute_line = datetime.datetime.now().minute\n    tobe_cached_bizs = []\n    for biz in biz_result:\n        bk_biz_id = biz[\"bk_biz_id\"]\n        if not biz_is_ready(bk_biz_id, minute_line):\n            continue\n        tobe_cached_bizs.append(bk_biz_id)\n        for cmdb_task in cmdb_api_list(bk_biz_id):\n            call_obj = cmdb_task[\"api\"]\n            api_coroutines.append(gevent_run(call_obj.request.refresh, pool, *cmdb_task[\"args\"], **cmdb_task[\"kwargs\"]))\n    if gevent_active:\n        for coroutine in api_coroutines:\n            coroutine.join()\n    logger.info(f\"[api cache] run cache with {len(tobe_cached_bizs)}bizs: {tobe_cached_bizs}\")\n    # 执行完任务后自杀\n    term_api_cron(immediately=True)\n\n\n@share_lock()\ndef term_api_cron(immediately=False):\n\n    if settings.IS_CONTAINER_MODE:\n        self_p = psutil.Process(os.getpid())\n        run_time = time.time() - self_p.create_time()\n        logger.info(f\"[api cache] scheduler:celery_worker_api_cron now running {run_time}s\")\n        if (immediately and run_time > 5 * CONST_MINUTES) or run_time > CONST_ONE_HOUR:\n            logger.info(\"[api cache] term_api_cron now\")\n            # celery 自杀\n            hostname = socket.gethostname()\n            app.control.broadcast(\"shutdown\", destination=[f\"celery@{hostname}\"])\n        return\n\n    # 二进制部署，用supervisor管理\n    s_client = get_supervisor_client()\n    process_info = s_client.supervisor.getProcessInfo(\"scheduler:celery_worker_api_cron\")\n    if process_info[\"statename\"] != \"RUNNING\":\n        return\n    # 1小时一次\n    run_time = process_info[\"now\"] - process_info[\"start\"]\n    logger.info(f\"[api cache] scheduler:celery_worker_api_cron now running {run_time}s\")\n    if (immediately and run_time > 5 * CONST_MINUTES) or run_time > CONST_ONE_HOUR:\n        # 保证最少跑5分钟\n        # 发送中断信号\n        # signal.SIGTERM\n        logger.info(\"[api cache] term_api_cron now\")\n        s_client.supervisor.signalProcess(\"scheduler:celery_worker_api_cron\", 15)\n\n\ndef biz_is_ready(bk_biz_id, minute_line):\n    if bk_biz_id == 0:\n        return False\n\n    client_index = HR.get_node(bk_biz_id)\n    return client_index == minute_line % RING_NODES\n\n\nAPI_CRONTAB = [\n    (cache_business, \"*/1 * * * *\"),\n    (cache_cmdb_resource, \"*/1 * * * *\"),\n    (term_api_cron, \"*/1 * * * *\")\n]\n\n\nRING_NODES = os.getenv(\"API_CACHE_BASE_INTERVAL\", \"5\")\ntry:\n    RING_NODES = int(RING_NODES)\nexcept (TypeError, ValueError):\n    RING_NODES = 5\n\n\ndef gevnet_task_with_close_connections(run, pool, *args, **kwargs):\n    \"\"\"\n    gevnet_task_with_close_db_connections\n    \"\"\"\n\n    def task_wrapper(func):\n        try:\n            return func(*args, **kwargs)\n        finally:\n            close_old_connections()\n            try:\n                mem_cache = caches[\"locmem\"]\n                mem_cache.clear()\n                del mem_cache\n            finally:\n                gc.collect()\n\n    if gevent_active:\n        return pool.spawn(task_wrapper, run)\n    else:\n        return task_wrapper(run)\n\n\ngevent_run = gevnet_task_with_close_connections\n\n# 初始化一个65535个虚拟节点，实际节点（0-4）的哈希环\nHR = HashRing(dict.fromkeys(list(range(RING_NODES)), 1))\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/bkmonitor/alarm_backends/core/api_cache/library.py b/bkmonitor/alarm_backends/core/api_cache/library.py
--- a/bkmonitor/alarm_backends/core/api_cache/library.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/bkmonitor/alarm_backends/core/api_cache/library.py	(date 1745303880171)
@@ -168,9 +168,9 @@
 
 
 API_CRONTAB = [
-    (cache_business, "*/1 * * * *"),
-    (cache_cmdb_resource, "*/1 * * * *"),
-    (term_api_cron, "*/1 * * * *")
+    # (cache_business, "*/1 * * * *"),
+    # (cache_cmdb_resource, "*/1 * * * *"),
+    # (term_api_cron, "*/1 * * * *")
 ]
 
 
Index: collect_process/retry_collectors/config.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\n/*\n * ------------------------------------------------------------------------------\n * *****************************************************************************\n * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2024\n *\n * The copyright to the computer program(s) herein is the property of\n * CMSS Co.,Ltd. The programs may be used and/or copied only with written\n * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions\n * stipulated in the agreement/contract under which the program(s) have been\n * supplied.\n * ******************************************************************************\n * -------------------------------------------------------------------------------\n */\n \"\"\"\n\nconfig = {\n    \"Cookie\": \"bk_token=NKylHjcUdNE_Kls_tvF_jPMHtcmb81ZamegxaAXGQ7I; blueking_language=zh-cn; enterprise_monitor_csrftoken=AjwbiZLC62CTbZhY8EpKWbduNRzJbmCd7HryssVRMXUyXQVTu33GlJhLgPz0wsyw; bk_monitorv3_sessionid=e8c1un0rwv6uinax12b34p8yefcz9f2k; bk_biz_id=228\",\n    \"User-Agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n    \"Content-Type\": \"application/json\",\n    \"X-Csrftoken\": \"uI6fepO6Vsu7tzNNN5AwAoBgBQAhR36Z161CoSYlBnMMfqrI9uesZWFx4OAyc92i\",\n    \"base_url\": \"http://bkmonitor.ccops.cmecloud.cn/\"\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/collect_process/retry_collectors/config.py b/collect_process/retry_collectors/config.py
--- a/collect_process/retry_collectors/config.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/collect_process/retry_collectors/config.py	(date 1745303880184)
@@ -15,9 +15,9 @@
  """
 
 config = {
-    "Cookie": "bk_token=NKylHjcUdNE_Kls_tvF_jPMHtcmb81ZamegxaAXGQ7I; blueking_language=zh-cn; enterprise_monitor_csrftoken=AjwbiZLC62CTbZhY8EpKWbduNRzJbmCd7HryssVRMXUyXQVTu33GlJhLgPz0wsyw; bk_monitorv3_sessionid=e8c1un0rwv6uinax12b34p8yefcz9f2k; bk_biz_id=228",
+    "Cookie": "blueking_language=zh-cn; enterprise_monitor_csrftoken=Dde9FLViLCPxZObxaMiIMrexl0ZrO1FIUzbi7hyFLsC3KR5usFyzkJJacyia9lJQ; bk_monitorv3_sessionid=2wi08xewv2bw352rcnx08qn0ceu2im4p; bk_token=LoipQnfLcMjXbxNH_QaQTXCD_KAzBjnPjzRv202GC4k; bk_biz_id=229",
     "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
     "Content-Type": "application/json",
-    "X-Csrftoken": "uI6fepO6Vsu7tzNNN5AwAoBgBQAhR36Z161CoSYlBnMMfqrI9uesZWFx4OAyc92i",
+    "X-Csrftoken": "VMJFuMN35gzGpycClhExbtL7zB6naboWc8GOWiqq56mcaB6zDaUoJLgKq9p6vvs4",
     "base_url": "http://bkmonitor.ccops.cmecloud.cn/"
 }
\ No newline at end of file
Index: bkmonitor/config/__init__.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># -*- coding: utf-8 -*-\n\"\"\"\nTencent is pleased to support the open source community by making BK-LOG 蓝鲸日志平台 available.\nCopyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.\nBK-LOG 蓝鲸日志平台 is licensed under the MIT License.\nLicense for BK-LOG 蓝鲸日志平台:\n--------------------------------------------------------------------\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated\ndocumentation files (the \"Software\"), to deal in the Software without restriction, including without limitation\nthe rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,\nand to permit persons to whom the Software is furnished to do so, subject to the following conditions:\nThe above copyright notice and this permission notice shall be included in all copies or substantial\nportions of the Software.\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT\nLIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\nNO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nWe undertake not to change the open source license (MIT license) applicable to the current version of\nthe project delivered to anyone in the future.\n\"\"\"\n\nimport os\n\n__author__ = u\"蓝鲸智云\"\n__copyright__ = \"Copyright © 2012-2019 Tencent BlueKing. All Rights Reserved.\"\n__all__ = [\"celery_app\", \"ENVIRONMENT\", \"RUN_VER\", \"APP_CODE\", \"SECRET_KEY\", \"BK_URL\", \"BASE_DIR\"]\n\n# This will make sure the app is always imported when\n# Django starts so that shared_task will use this app.\nfrom blueapps.core.celery import celery_app\n\n\ndef get_env_or_raise(*keys, default=None):\n    \"\"\"Get an environment variable, if it does not exist, raise an exception\"\"\"\n    for key in keys:\n        value = os.environ.get(key)\n        if value:\n            return value\n\n    if default is not None:\n        return default\n\n    raise RuntimeError(\n        'Environment variable \"{}\" not found, you must set this variable to run this application.'.format(keys)\n    )\n\n\n# SaaS运行版本，如非必要请勿修改\nRUN_VER = os.environ.get(\"BKPAAS_ENGINE_REGION\", \"open\")\n\nAPP_CODE = get_env_or_raise(\"APP_ID\", \"BKPAAS_APP_ID\", \"BK_MONITOR_APP_CODE\", \"APP_CODE\", default=\"bkmonitorv3\")\n# 应用用于调用云 API 的 Secret\nSECRET_KEY = get_env_or_raise(\"APP_TOKEN\", \"BKPAAS_APP_SECRET\", \"BK_MONITOR_APP_SECRET\", \"APP_SECRET\", default=\"\")\n\n\n# V3判断环境的环境变量为BKPAAS_ENVIRONMENT\nif \"BKPAAS_ENVIRONMENT\" in os.environ:\n    ENVIRONMENT = os.getenv(\"BKPAAS_ENVIRONMENT\", \"dev\")\n# V2判断环境的环境变量为BK_ENV\nelse:\n    PAAS_V2_ENVIRONMENT = os.environ.get(\"BK_ENV\", \"development\")\n    ENVIRONMENT = {\n        \"development\": \"dev\",\n        \"testing\": \"stag\",\n        \"production\": \"prod\",\n    }.get(PAAS_V2_ENVIRONMENT)\n\n\n# 蓝鲸平台URL\nBK_URL = os.getenv(\"BKPAAS_URL\", None)  # noqa\n\n# Build paths inside the project like this: os.path.join(BASE_DIR, ...)\nBASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n\n# read VERSION file\ntry:\n    VERSION = open(os.path.join(BASE_DIR, \"VERSION\"), \"r\", encoding=\"utf-8\").read().strip()\nexcept:  # noqa\n    VERSION = \"Unknown version\"\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/bkmonitor/config/__init__.py b/bkmonitor/config/__init__.py
--- a/bkmonitor/config/__init__.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/bkmonitor/config/__init__.py	(date 1745303880201)
@@ -22,6 +22,9 @@
 
 import os
 
+import pymysql
+pymysql.install_as_MySQLdb()
+
 __author__ = u"蓝鲸智云"
 __copyright__ = "Copyright © 2012-2019 Tencent BlueKing. All Rights Reserved."
 __all__ = ["celery_app", "ENVIRONMENT", "RUN_VER", "APP_CODE", "SECRET_KEY", "BK_URL", "BASE_DIR"]
Index: bkmonitor/alarm_backends/service/scheduler/tasks/report_cron.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># -*- coding: utf-8 -*-\n\"\"\"\nTencent is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.\nCopyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.\nLicensed under the MIT License (the \"License\"); you may not use this file except in compliance with the License.\nYou may obtain a copy of the License at http://opensource.org/licenses/MIT\nUnless required by applicable law or agreed to in writing, software distributed under the License is distributed on\nan \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the\nspecific language governing permissions and limitations under the License.\n\"\"\"\n\nimport logging\n\nfrom celery.schedules import crontab\nfrom celery.task import periodic_task\nfrom django.conf import settings\n\nfrom alarm_backends.service.report.tasks import (\n    operation_data_custom_report_v2,\n    report_mail_detect,\n    report_transfer_operation_data,\n)\nfrom alarm_backends.service.scheduler.tasks.cron import task_duration\nfrom bkmonitor.utils import custom_report_aggate\nfrom metadata.task.config_refresh import refresh_es_storage\n\nlogger = logging.getLogger(\"bkmonitor.cron_report\")\n\nREPORT_CRONTAB = [\n    (operation_data_custom_report_v2, \"*/5 * * * *\"),\n    (report_transfer_operation_data, \"*/5 * * * *\"),\n    (refresh_es_storage, \"*/10 * * * *\"),  # NOTE: ES 周期性任务先放到当前队列中\n    # SLI指标和运营数据调整到report周期任务\n    (custom_report_aggate.fetch_sli_data_and_push, \"* * * * *\"),\n    (custom_report_aggate.fetch_operation_data_and_push, \"*/5 * * * *\"),\n]\n\nif int(settings.MAIL_REPORT_BIZ):\n    # 如果配置了订阅报表默认业务\n    # 订阅报表定时任务 REPORT_CRONTAB\n    REPORT_CRONTAB.append((report_mail_detect, \"*/1 * * * *\"))\n\nfor func, cron_expr in REPORT_CRONTAB:\n    cron_list = cron_expr.split()\n    new_func = task_duration(func.__name__)(func)\n    locals()[new_func.__name__] = periodic_task(\n        run_every=crontab(*cron_list),\n        ignore_result=True,\n        queue=\"celery_report_cron\",\n        expires=300,  # The task will not be executed after the expiration time.\n    )(new_func)\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/bkmonitor/alarm_backends/service/scheduler/tasks/report_cron.py b/bkmonitor/alarm_backends/service/scheduler/tasks/report_cron.py
--- a/bkmonitor/alarm_backends/service/scheduler/tasks/report_cron.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/bkmonitor/alarm_backends/service/scheduler/tasks/report_cron.py	(date 1745303880211)
@@ -27,12 +27,12 @@
 logger = logging.getLogger("bkmonitor.cron_report")
 
 REPORT_CRONTAB = [
-    (operation_data_custom_report_v2, "*/5 * * * *"),
-    (report_transfer_operation_data, "*/5 * * * *"),
-    (refresh_es_storage, "*/10 * * * *"),  # NOTE: ES 周期性任务先放到当前队列中
-    # SLI指标和运营数据调整到report周期任务
-    (custom_report_aggate.fetch_sli_data_and_push, "* * * * *"),
-    (custom_report_aggate.fetch_operation_data_and_push, "*/5 * * * *"),
+    # (operation_data_custom_report_v2, "*/5 * * * *"),
+    # (report_transfer_operation_data, "*/5 * * * *"),
+    # (refresh_es_storage, "*/10 * * * *"),  # NOTE: ES 周期性任务先放到当前队列中
+    # # SLI指标和运营数据调整到report周期任务
+    # (custom_report_aggate.fetch_sli_data_and_push, "* * * * *"),
+    # (custom_report_aggate.fetch_operation_data_and_push, "*/5 * * * *"),
 ]
 
 if int(settings.MAIL_REPORT_BIZ):
Index: collect_process/retry_collectors/retry_collect.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\n/*\n * ------------------------------------------------------------------------------\n * *****************************************************************************\n * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2024\n *\n * The copyright to the computer program(s) herein is the property of\n * CMSS Co.,Ltd. The programs may be used and/or copied only with written\n * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions\n * stipulated in the agreement/contract under which the program(s) have been\n * supplied.\n * ******************************************************************************\n * -------------------------------------------------------------------------------\n */\n \"\"\"\n\nimport os\nimport json\nimport time\n\nimport requests\nfrom config import config\n\nheaders = {\n    \"User-Agent\":  config[\"User-Agent\"],\n    \"Content-Type\": config[\"Content-Type\"],\n    \"Cookie\": config[\"Cookie\"],\n    \"X-Csrftoken\": config[\"X-Csrftoken\"]\n}\n\ninstance_url = \"rest/v2/collecting_config/collect_instance_status/\"\nretry_url = \"rest/v2/collecting_config/retry/\"\n\n\ndef get_instance(instance_url, ids):\n    retry_data = []\n    for id in ids:\n        id_url = instance_url + \"?id=\" + str(id)\n        print(id_url)\n        url = os.path.join(config[\"base_url\"], id_url)\n        # print(url)\n        instance_res = requests.get(url, headers=headers)\n        result = json.loads(instance_res.content)\n\n        if result[\"code\"] == 200:\n            contents = result[\"data\"][\"contents\"]\n            config_info = result[\"data\"][\"config_info\"]\n            # print(contents)\n            for content in contents:\n                # print(content)\n                for child in content[\"child\"]:\n                    if child[\"status\"] == \"FAILED\":\n                        # print(child)\n                        retry_data.append({\"instance_id\": child[\"instance_id\"], \"ip\": child[\"ip\"], \"id\": id,\n                                           \"task_id\": child[\"task_id\"], \"name\": config_info[\"name\"],\n                                           \"bk_biz_id\": config_info[\"bk_biz_id\"]})\n\n    return retry_data\n\n\ndef retry_col(retry_data, retry_url):\n    print(\"本采集总异常数是： \", len(retry_data))\n\n    url = os.path.join(config[\"base_url\"], retry_url)\n    # print(url)\n    for data in retry_data:\n        try:\n            result = requests.post(url, data=json.dumps(data), headers=headers)\n\n            result = json.loads(result.content)\n            print(result, data)\n\n            # if result[\"code\"] == 200 and result[\"message\"] == \"OK\":\n            #     # time.sleep(1)\n            # else:\n            #     re = result[\"message\"] if \"message\" in result else \"error\"\n            #     print(re)\n        except Exception as e:\n            print(e)\n\nids = [12331]\n\nretry_data = get_instance(instance_url, ids)\nretry_col(retry_data, retry_url)\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/collect_process/retry_collectors/retry_collect.py b/collect_process/retry_collectors/retry_collect.py
--- a/collect_process/retry_collectors/retry_collect.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/collect_process/retry_collectors/retry_collect.py	(date 1745303880228)
@@ -29,7 +29,7 @@
 }
 
 instance_url = "rest/v2/collecting_config/collect_instance_status/"
-retry_url = "rest/v2/collecting_config/retry/"
+retry_url = "rest/v2/collecting_config/revoke/"
 
 
 def get_instance(instance_url, ids):
@@ -49,9 +49,9 @@
             for content in contents:
                 # print(content)
                 for child in content["child"]:
-                    if child["status"] == "FAILED":
+                    if child["status"] == "DEPLOYING":
                         # print(child)
-                        retry_data.append({"instance_id": child["instance_id"], "ip": child["ip"], "id": id,
+                        retry_data.append({"instance_ids": [child["instance_id"]], "ip": child["ip"], "id": id,
                                            "task_id": child["task_id"], "name": config_info["name"],
                                            "bk_biz_id": config_info["bk_biz_id"]})
 
@@ -78,7 +78,7 @@
         except Exception as e:
             print(e)
 
-ids = [12331]
+ids = [10880]
 
 retry_data = get_instance(instance_url, ids)
 retry_col(retry_data, retry_url)
Index: bkmonitor/config/role/api.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># -*- coding: utf-8 -*-\n\"\"\"\nTencent is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.\nCopyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.\nLicensed under the MIT License (the \"License\"); you may not use this file except in compliance with the License.\nYou may obtain a copy of the License at http://opensource.org/licenses/MIT\nUnless required by applicable law or agreed to in writing, software distributed under the License is distributed on\nan \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the\nspecific language governing permissions and limitations under the License.\n\"\"\"\nimport logging\n\nfrom ..tools.environment import NEW_ENV\n\n# 按照环境变量中的配置，加载对应的配置文件\ntry:\n    _module = __import__(f\"config.{NEW_ENV}\", globals(), locals(), [\"*\"])\nexcept ImportError as e:\n    logging.exception(e)\n    raise ImportError(\"Could not import config '{}' (Is it on sys.path?): {}\".format(NEW_ENV, e))\n\nfrom config.role.web import *  # noqa\nfrom config.role.worker import *  # noqa\n\nfor _setting in dir(_module):\n    if _setting == _setting.upper():\n        locals()[_setting] = getattr(_module, _setting)\n\nMIGRATE_MONITOR_API = False\n\nINSTALLED_APPS += (\n    \"django_elasticsearch_dsl\",\n    \"rest_framework\",\n    \"django_filters\",\n    \"bkmonitor\",\n    \"bkm_space\",\n    \"monitor\",\n    \"monitor_api\",\n    \"monitor_web\",\n    \"apm_web\",\n    \"apm\",\n    \"fta_web\",\n    \"kernel_api\",\n    \"metadata\",\n    \"calendars\",\n    \"core.drf_resource\",\n    \"django_celery_beat\",\n    \"django_celery_results\",\n    \"audit\",\n    \"apigw_manager\",\n)\n\n# api not use celery worker\nCELERY_ALWAYS_EAGER = True\n\nLOGGER_LEVEL = os.environ.get(\"BKAPP_LOG_LEVEL\", \"INFO\")\nLOGGER_DEFAULT = {\n    \"level\": LOGGER_LEVEL,\n    \"propagate\": False,\n    \"handlers\": [\"file\", \"console\"],\n}\n\n\ndef get_logger_config(log_path, logger_level, log_file_prefix):\n    return {\n        \"version\": 1,\n        \"loggers\": {\n            \"\": {\"level\": \"ERROR\", \"handlers\": [\"file\", \"console\"]},\n            \"django.request\": {\"handlers\": [\"file\", \"console\"], \"level\": \"ERROR\", \"propagate\": True},\n            \"monitor\": LOGGER_DEFAULT,\n            \"monitor_api\": LOGGER_DEFAULT,\n            \"utils\": LOGGER_DEFAULT,\n            \"drf_non_orm\": LOGGER_DEFAULT,\n            \"common\": LOGGER_DEFAULT,\n            \"monitor_adapter\": LOGGER_DEFAULT,\n            \"kernel_api\": LOGGER_DEFAULT,\n            \"project\": LOGGER_DEFAULT,\n            \"bkmonitor\": LOGGER_DEFAULT,\n            \"kernel\": LOGGER_DEFAULT,\n            \"metadata\": LOGGER_DEFAULT,\n            \"kubernetes\": LOGGER_DEFAULT,\n            \"apm\": LOGGER_DEFAULT,\n            \"calendars\": LOGGER_DEFAULT,\n            \"sql_parse\": LOGGER_DEFAULT,\n            \"file-only\": {\"level\": LOGGER_LEVEL, \"propagate\": False, \"handlers\": [\"file\"]},\n            \"console-only\": {\"level\": LOGGER_LEVEL, \"propagate\": False, \"handlers\": [\"console\"]},\n        },\n        \"handlers\": {\n            \"console\": {\"class\": \"logging.StreamHandler\", \"level\": \"DEBUG\", \"formatter\": \"standard\"},\n            \"file\": {\n                \"class\": \"logging.handlers.WatchedFileHandler\",\n                \"level\": \"DEBUG\",\n                \"formatter\": \"standard\",\n                \"filename\": os.path.join(log_path, f\"{log_file_prefix}kernel_api.log\"),\n                \"encoding\": \"utf-8\",\n            },\n        },\n        \"formatters\": {\n            \"standard\": {\n                \"format\": (\n                    \"%(asctime)s %(levelname)-8s %(process)-8d\" \"%(name)-15s %(filename)20s[%(lineno)03d] %(message)s\"\n                ),\n                \"datefmt\": \"%Y-%m-%d %H:%M:%S\",\n            }\n        },\n    }\n\n\nLOGGING = LOGGER_CONF = get_logger_config(LOG_PATH, LOGGER_LEVEL, LOG_FILE_PREFIX)\n\n\nif IS_CONTAINER_MODE:\n    for logger in LOGGING[\"loggers\"]:\n        if \"null\" not in LOGGING[\"loggers\"][logger][\"handlers\"]:\n            LOGGING[\"loggers\"][logger][\"handlers\"] = [\"console\"]\n\n#\n# Templates\n#\nTEMPLATE_CONTEXT_PROCESSORS = (\n    # the context to the templates\n    \"django.contrib.auth.context_processors.auth\",\n    \"django.template.context_processors.request\",\n    \"django.template.context_processors.csrf\",\n    \"django.contrib.messages.context_processors.messages\",\n    \"common.context_processors.get_context\",  # 自定义模版context，可以在页面中使用STATIC_URL等变量\n    \"django.template.context_processors.i18n\",\n)\nTEMPLATE_DIRS = (\n    os.path.join(BASE_DIR, \"kernel_api/templates\"),  # noqa\n    os.path.join(BASE_DIR, \"bkmonitor/templates\"),  # noqa\n)\nTEMPLATES = [\n    {\n        \"NAME\": \"jinja2\",\n        \"BACKEND\": \"django_jinja.backend.Jinja2\",\n        \"DIRS\": [\"templates\"],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"match_extension\": \".jinja\",\n            \"context_processors\": [\n                \"django.template.context_processors.i18n\",\n                \"django.contrib.messages.context_processors.messages\",\n            ],\n            \"undefined\": DebugUndefined,\n        },\n    },\n    {\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\n        \"DIRS\": list(TEMPLATE_DIRS),\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\"context_processors\": list(TEMPLATE_CONTEXT_PROCESSORS)},\n    },\n]\n\nROOT_URLCONF = \"kernel_api.urls\"\nMIDDLEWARE = (\n    \"bkmonitor.middlewares.prometheus.MetricsBeforeMiddleware\",  # 必须放到最前面\n    \"django.contrib.sessions.middleware.SessionMiddleware\",\n    \"django.middleware.locale.LocaleMiddleware\",\n    \"django.middleware.common.CommonMiddleware\",\n    # 'django.middleware.csrf.CsrfViewMiddleware',\n    \"django.contrib.auth.middleware.AuthenticationMiddleware\",\n    \"django.contrib.messages.middleware.MessageMiddleware\",\n    \"django.middleware.security.SecurityMiddleware\",\n    \"blueapps.middleware.request_provider.RequestProvider\",\n    \"bkmonitor.middlewares.request_middlewares.RequestProvider\",\n    \"kernel_api.middlewares.ApiTimeZoneMiddleware\",\n    \"kernel_api.middlewares.ApiLanguageMiddleware\",\n    \"kernel_api.middlewares.authentication.AuthenticationMiddleware\",\n    \"bkm_space.middleware.ParamInjectMiddleware\",\n    \"bkmonitor.middlewares.prometheus.MetricsAfterMiddleware\",  # 必须放到最后面\n)\n\nREST_FRAMEWORK = {\n    \"DEFAULT_FILTER_BACKENDS\": (\n        \"django_filters.rest_framework.DjangoFilterBackend\",\n        \"rest_framework.filters.OrderingFilter\",\n    ),\n    \"DEFAULT_RENDERER_CLASSES\": (\"kernel_api.adapters.ApiRenderer\",),\n    \"DEFAULT_AUTHENTICATION_CLASSES\": (\"kernel_api.middlewares.authentication.KernelSessionAuthentication\",),\n    # 'DATETIME_FORMAT': \"%Y-%m-%d %H:%M:%S\",\n    \"EXCEPTION_HANDLER\": \"kernel_api.exceptions.api_exception_handler\",\n    \"DEFAULT_PAGINATION_CLASS\": \"bkmonitor.views.pagination.MonitorAPIPagination\",\n    \"PAGE_SIZE\": 20,\n    \"DEFAULT_PERMISSION_CLASSES\": (),\n}\n\nRESOURCE_PROXY_TEMPLATE = \"{module}.project.{path}\"\n\n#\n# Authentication & Authorization\n#\nAUTH_USER_MODEL = \"account.User\"\n\nAUTHENTICATION_BACKENDS = (\n    \"kernel_api.middlewares.authentication.AppWhiteListModelBackend\",\n    \"blueapps.account.backends.UserBackend\",\n)\n\nALLOW_EXTEND_API = True\nAPIGW_PUBLIC_KEY = \"\"\nAES_X_KEY_FIELD = \"SAAS_SECRET_KEY\"\n\n# 跳过权限中心检查\nSKIP_IAM_PERMISSION_CHECK = True\n\n# 重启服务器时清除缓存\nCLEAR_CACHE_ON_RESTART = False\n\n# esb组件地址\nCOMMON_USERNAME = os.environ.get(\"BK_ESB_SUPER_USER\", \"admin\")\n\n# 节点管理数据库，仅当监控SaaS与节点管理公用DB实例时适用\n# TODO: smart v3应用需要进行额外配置\nDATABASES[\"nodeman\"] = {}\nDATABASES[\"nodeman\"].update(DATABASES[\"default\"])\nDATABASES[\"nodeman\"][\"NAME\"] = os.environ.get(\"BKAPP_NODEMAN_DB_NAME\", \"bk_nodeman\")\n\nAES_TOKEN_KEY = os.environ.get(\"AK_AES_TOKEN_KEY\", \"ALERT_RESULT\")\n\nINGESTER_CONSUL = os.environ.get(\"INGESTER_CONSUL\", \"\")\n\n# 项目空间API类模块路径\nBKM_SPACE_API_CLASS = \"metadata.resources.space_api.InjectSpaceApi\"\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/bkmonitor/config/role/api.py b/bkmonitor/config/role/api.py
--- a/bkmonitor/config/role/api.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/bkmonitor/config/role/api.py	(date 1745303880236)
@@ -65,25 +65,7 @@
     return {
         "version": 1,
         "loggers": {
-            "": {"level": "ERROR", "handlers": ["file", "console"]},
-            "django.request": {"handlers": ["file", "console"], "level": "ERROR", "propagate": True},
-            "monitor": LOGGER_DEFAULT,
-            "monitor_api": LOGGER_DEFAULT,
-            "utils": LOGGER_DEFAULT,
-            "drf_non_orm": LOGGER_DEFAULT,
-            "common": LOGGER_DEFAULT,
-            "monitor_adapter": LOGGER_DEFAULT,
-            "kernel_api": LOGGER_DEFAULT,
-            "project": LOGGER_DEFAULT,
-            "bkmonitor": LOGGER_DEFAULT,
-            "kernel": LOGGER_DEFAULT,
-            "metadata": LOGGER_DEFAULT,
-            "kubernetes": LOGGER_DEFAULT,
-            "apm": LOGGER_DEFAULT,
-            "calendars": LOGGER_DEFAULT,
-            "sql_parse": LOGGER_DEFAULT,
-            "file-only": {"level": LOGGER_LEVEL, "propagate": False, "handlers": ["file"]},
-            "console-only": {"level": LOGGER_LEVEL, "propagate": False, "handlers": ["console"]},
+            "": {"level": "DEBUG", "handlers": ["file", "console"]},
         },
         "handlers": {
             "console": {"class": "logging.StreamHandler", "level": "DEBUG", "formatter": "standard"},
@@ -223,3 +205,8 @@
 
 # 项目空间API类模块路径
 BKM_SPACE_API_CLASS = "metadata.resources.space_api.InjectSpaceApi"
+
+logger = logging.getLogger(__name__)
+print(__name__)
+print("-------------------------------------------------------------------------------ttttt")
+logger.info("-------------------------------------------------------------------------------log")
Index: collect_process/zhengli.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\n/*\n * ------------------------------------------------------------------------------\n * *****************************************************************************\n * COPYRIGHT China Mobile (SuZhou) Software Technology Co.,Ltd. 2024\n *\n * The copyright to the computer program(s) herein is the property of\n * CMSS Co.,Ltd. The programs may be used and/or copied only with written\n * permission from CMSS Co.,Ltd. or in accordance with the terms and conditions\n * stipulated in the agreement/contract under which the program(s) have been\n * supplied.\n * ******************************************************************************\n * -------------------------------------------------------------------------------\n */\n \"\"\"\n# import json\n#\n# from config import du, bingxing, more_5000\n#\n# fnew_id = open(\"./need_select/new_select_sql.json\", \"r\")\n# new_id = json.load(fnew_id)\n#\n# for new in new_id:\n#     if new_id[new][\"old_collect_id\"] in more_5000:\n#         print(new_id[new][\"bk_biz_id\"], \",\", new_id[new][\"old_collect_id\"], \",\", new_id[new][\"name\"])\n\nimport base64\nimport re\n\ninstructions = ['kill', 'vipw', 'systemctl reboot', 'edit', 'fdisk /dev/*', 'R(^update.*)', 'iptables -D', '/etc/init.d/network', 'insert', 'systemctl rescue', 'set image', 'xfs_repair', 'vim', 'truncate', 'start', 'ip6tables -D', 'pwunconv', 'ltrim', 'patch', 'ssh', 'R([\\\\ ]+rm[\\\\ ]+)', 'password', 'R(^ping.*169.254)', 'reload', 'groupmod', 'drop', 'tcpdump', 'pwck', 'init', 'source', 'mysql -p', 'history -c', 'del', 'update', 'set', 'delete', 'umount', 'root', 'poweroff', 'route del', 'route add', 'py', 'chpasswd', 'mkisofs', 'append', 'R(^\\\\s*sh\\\\s.*)', 'bootstrap', 'chown * /', 'groupmems', 'create', 'fsck', 'apply', 'adduser', 'systemctl halt', 'lock', 'grpunconv', 'vi', 'iptables', 'systemctl hibernate', 'systemctl * network', 'newusers', 'perisist', 'keys', 'lpush', 'stop', 'halt', 'mount', '.sh', 'mysqldump', 'swapon', 'rm', 'passwd', 'mqshutdown', 'pwconv', 'systemctl suspend', 'useradd', 'parted', 'ifup', 'chmod * /', 'uncordon', 'flushdb', 'scp', 'python', 'mv', 'add', 'R(ifconfig.*down)', 'restart', 'revoke', 'R(.*+manifest\\\\s+.*)', 'delete*', 'curl', 'shutdown', 'config', 'usermod', 'ip route del', 'curl -u', 'grpconv', 'ifdown', 'groupadd', 'flushall', 'R([\\\\ ]+kill[\\\\ ]+)', 'R(^kill\\\\ )', 'reboot', 'rollout', 'slave_skip_errors', 'scale', 'R(^rm[\\\\ ]+)', 'cordon', 'incrby', 'systemctl hybrid-sleep', 'iptables -I', 'ip route add', 'systemctl kexec', 'route -6 add', 'xtrabackup --copy-back --target-dir', 'linsert', 'wget', 'ip6tables -I', 'rm * /', 'su', 'vi /etc/sudoers.d/*', 'mset', 'vim /etc/sudoers.d/*', 'iptables -A', 'history', 'grpck', 'ip6tables -A', 'rpop', 'flush', 'vigr', 'userdel', 'groupdel', 'systemctl poweroff', 'sysctl', 'curl -d', 'systemctl emergency', 'alter', 'R(.*mysql\\\\s+.*)', 'R(^\\\\s*systemctl\\\\s+start\\\\s+NetworkManager\\\\s*$)', 'R(^\\\\s*sudo\\\\s*history\\\\s+-d.*)', 'R(^\\\\s*sudo\\\\s*history\\\\s+-c.*)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+emergency.*)', 'R(^\\\\s*ping.*169.254.*)', 'R(^\\\\s*(truncate|TRUNCATE).*$)', 'R((^\\\\s*sudo\\\\s+fsck.*)|(^\\\\s*fsck.*))', 'R(^\\\\s*(drop|DROP).*$)', 'R(^\\\\s*systemctl\\\\s+stop\\\\s+network\\\\s*$)', 'R((.*\\\\s+kill.*)|(^\\\\s*kill.*))', '/etc/init.d/network', 'R(^\\\\s*vi\\\\s+/etc/sudoers.d/.*)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+halt.*)', 'R((^\\\\s*sudo\\\\s+KILL.*)|(^\\\\s*KILL.*))', 'R((^\\\\s*sudo\\\\s*sz\\\\s+)|(^\\\\s*sz\\\\s+))', 'R((^\\\\s*sudo\\\\s+reboot.*)|(^\\\\s*reboot.*))', 'R((^\\\\s*sudo\\\\s+groupdel.*)|(^\\\\s*groupdel.*))', 'R(^\\\\s*sudo\\\\s+screen\\\\s+-S\\\\s+test)', 'R((^\\\\s*sudo\\\\s+halt.*)|(^\\\\s*halt.*))', 'R((^\\\\s*sudo\\\\s*ssh\\\\s*)|(^\\\\s*ssh\\\\s*))', 'R(^curl\\\\s+-u.*$)', 'R(^\\\\s*ip\\\\s+route\\\\s+del.*)', 'R(^\\\\s*/bin/sh.*)', 'R(^\\\\s*sudo\\\\s+vim\\\\s+/etc/sudoers.d/.*)', 'R(^su$)', 'R(^.*manifests.*$)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+stop\\\\s+network\\\\s*$)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+restart\\\\s+Net.*M.*\\\\s*$)', 'R(^sudo\\\\s+curl\\\\s+-u.*$)', 'R((^\\\\s*sudo\\\\s+chmod.*)|(^\\\\s*chmod.*))', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+reset-failed.*)', 'R(.*\\\\s+stop.*$)', 'R((^\\\\s*sudo\\\\s+rm\\\\s+)|(^\\\\s*rm\\\\s+))', 'R((^\\\\s*sudo\\\\s+useradd.*)|(^\\\\s*useradd.*))', 'R(.*mongo\\\\s+.*)', 'R(^\\\\s*sudo\\\\s+sh\\\\s+-c\\\\s+.*)', 'R(^\\\\s*route\\\\s+del.*)', 'R(.*\\\\s+(delete|DELETE).*$)', 'R((^\\\\s*sudo\\\\s+ftp.*)|(^\\\\s*ftp.*))', 'R(.*psql\\\\s+.*)', 'R(^.*vim\\\\s+.*auditcmd.log)', 'R(^\\\\s*(flushall|FLUSHALL).*$)', 'R(^\\\\s*sudo\\\\s+iptables\\\\s+-D.*)', 'R(^\\\\s*(flushdb|FLUSHDB).*$)', 'R(^.*vi\\\\s+.*auditcmd.log)', 'R((^\\\\s*sudo\\\\s+xfs_repair.*)|(^\\\\s*xfs_repair.*))', 'R(^\\\\s*iptables\\\\s+-D.*)', 'R(^\\\\s*systemctl\\\\s+reset-failed.*)', 'R(.*redis-.*-a.*$)', 'R(^\\\\s*sudo\\\\s+/bin/sh.*)', 'R(^\\\\s*sudo\\\\s+ping.*169.254.*)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+poweroff.*)', 'R(^\\\\s*(insert|INSERT).*$)', 'R((^\\\\s*sudo\\\\s+scp.*)|(^\\\\s*scp.*))', 'R((^\\\\s*sudo\\\\s+ansible.*)|(^\\\\s*ansible.*))', 'R(^\\\\s*systemctl\\\\s+start\\\\s+network\\\\s*$)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+rescue.*)', 'R(^\\\\s*sudo\\\\s+iptables\\\\s+-I.*)', 'R(^\\\\s*systemctl\\\\s+emergency.*)', 'R((^\\\\s*sudo\\\\s+poweroff.*)|(^\\\\s*poweroff.*))', 'R(^\\\\s*systemctl\\\\s+poweroff.*)', 'R((^\\\\s*sudo\\\\s*su\\\\s*)|(^\\\\s*su\\\\s+))', 'R(^\\\\s*(alter|ALTER).*$)', 'R(^\\\\s*vim\\\\s+/etc/sudoers.d/.*)', 'R((^\\\\s*sudo\\\\s+shutdown.*)|(^\\\\s*shutdown.*))', 'R(^\\\\s*(update|UPDATE).*$)', 'R(^\\\\s*systemctl\\\\s+reboot.*)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+restart\\\\s+network\\\\s*$)', 'R(^\\\\s*sudo\\\\s+ip\\\\s+route\\\\s+del.*)', 'R(^\\\\s*history\\\\s+-c.*)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+reboot.*)', 'R(^\\\\s*iptables\\\\s+-I.*)', 'R(^\\\\s*systemctl\\\\s+rescue.*)', 'R((.*\\\\s+sftp.*)|(^\\\\s*sftp.*))', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+stop\\\\s+Net.*M.*\\\\s*$)', 'R(^\\\\s*systemctl\\\\s+stop\\\\s+NetworkManager\\\\s*$)', 'R((^\\\\s*sudo\\\\s+init\\\\s+.*)|(^\\\\s*init\\\\s+.*))', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+start\\\\s+Net.*M.*\\\\s*$)', 'R((^\\\\s*sudo\\\\s+sftp.*)|(^\\\\s*sftp.*))', 'R(^\\\\s*history\\\\s+-d.*)', 'R(^\\\\s*sudo\\\\s+systemctl\\\\s+start\\\\s+network\\\\s*$)', 'R((.*\\\\s+shutdown.*)|(^\\\\s*shutdown.*))', 'R(^\\\\s*systemctl\\\\s+restart\\\\s+network\\\\s*$)', 'R(^\\\\s*systemctl\\\\s+halt.*)', 'R(^\\\\s*sudo\\\\s+vi\\\\s+/etc/sudoers.d/.*)', 'R(^\\\\s*sudo\\\\s+route\\\\s+del.*)', 'R((^\\\\s*sudo\\\\s+userdel.*)|(^\\\\s*userdel.*))', 'R(^\\\\s*sh\\\\s+-c\\\\s+.*)', 'R(^\\\\s*hostnamectl\\\\s+set-hostname.*)', 'R((pgsql$)|(my$)|(sqlcmd$))', 'R(^\\\\s*sudo\\\\s+hostnamectl\\\\s+set-hostname.*)', 'R((^\\\\s*sudo\\\\s+passwd.*)|(^\\\\s*passwd.*))', 'R(^\\\\s*systemctl\\\\s+restart\\\\s+NetworkManager\\\\s*$)']\nscript_content = \"IyEvYmluL2Jhc2gKZm9yIGFwaXNlcnZlck5hbWUgaW4gJChzdWRvIC9hcHBzL2Jpbi9rdWJlY3RsIGdldCBwb2QgLW4ga3ViZS1zeXN0ZW0gfGdyZXAgYXBpc2VydmVyfGF3ayAne3ByaW50ICQxfScpCmRvCglTdGFydGVkX3RpbWU9JChzdWRvIC9hcHBzL2Jpbi9rdWJlY3RsIGRlc2NyaWJlIHBvZCAtbiBrdWJlLXN5c3RlbSAke2FwaXNlcnZlck5hbWV9IHxncmVwIFN0YXJ0ZWR8YXdrIC1GICJTdGFydGVkOiIgJ3twcmludCAkMn0nfHhhcmdzKQoJU3RhcnRlZF9kYXRlPSQoZGF0ZSAtZCBAJChkYXRlIC1kICIke1N0YXJ0ZWRfdGltZX0iICslcykgKyIlWS0lbS0lZCIpCglUb2RheV9kYXRlPSQoZGF0ZSArIiVZLSVtLSVkIikKCQoJaWYgW1sgIiRTdGFydGVkX2RhdGUiID09ICIkVG9kYXlfZGF0ZSIgXV07IHRoZW4KCQllY2hvICdBcGlTdGFydFN0YXR1c3tQb2ROYW1lPSdcIiR7YXBpc2VydmVyTmFtZX1cIicsVG9kYXlfZGF0ZT0nXCIke1RvZGF5X2RhdGV9XCInLFN0YXJ0ZWRfZGF0ZT0nXCIke1N0YXJ0ZWRfZGF0ZX1cIid9JyAxCgllbHNlCgkJZWNobyAnQXBpU3RhcnRTdGF0dXN7UG9kTmFtZT0nXCIke2FwaXNlcnZlck5hbWV9XCInLFRvZGF5X2RhdGU9J1wiJHtUb2RheV9kYXRlfVwiJyxTdGFydGVkX2RhdGU9J1wiJHtTdGFydGVkX2RhdGV9XCInfScgMAoJZmkKCmRvbmU=\"\nscript_content = str(base64.b64decode(script_content), 'utf-8')\nscript_content = script_content.split('\\n')\n\nfor content in script_content:\n    if content.startswith('#'):\n        continue\n\n    for ins in instructions:\n        print(ins)\n        if re.search(\"R(.*+manifest\\s+.*)\", content):\n            print(111)\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/collect_process/zhengli.py b/collect_process/zhengli.py
--- a/collect_process/zhengli.py	(revision 7f8426b782a12e067e31995bb5eadfcff0a7be1b)
+++ b/collect_process/zhengli.py	(date 1745303880247)
@@ -28,15 +28,16 @@
 import re
 
 instructions = ['kill', 'vipw', 'systemctl reboot', 'edit', 'fdisk /dev/*', 'R(^update.*)', 'iptables -D', '/etc/init.d/network', 'insert', 'systemctl rescue', 'set image', 'xfs_repair', 'vim', 'truncate', 'start', 'ip6tables -D', 'pwunconv', 'ltrim', 'patch', 'ssh', 'R([\\ ]+rm[\\ ]+)', 'password', 'R(^ping.*169.254)', 'reload', 'groupmod', 'drop', 'tcpdump', 'pwck', 'init', 'source', 'mysql -p', 'history -c', 'del', 'update', 'set', 'delete', 'umount', 'root', 'poweroff', 'route del', 'route add', 'py', 'chpasswd', 'mkisofs', 'append', 'R(^\\s*sh\\s.*)', 'bootstrap', 'chown * /', 'groupmems', 'create', 'fsck', 'apply', 'adduser', 'systemctl halt', 'lock', 'grpunconv', 'vi', 'iptables', 'systemctl hibernate', 'systemctl * network', 'newusers', 'perisist', 'keys', 'lpush', 'stop', 'halt', 'mount', '.sh', 'mysqldump', 'swapon', 'rm', 'passwd', 'mqshutdown', 'pwconv', 'systemctl suspend', 'useradd', 'parted', 'ifup', 'chmod * /', 'uncordon', 'flushdb', 'scp', 'python', 'mv', 'add', 'R(ifconfig.*down)', 'restart', 'revoke', 'R(.*+manifest\\s+.*)', 'delete*', 'curl', 'shutdown', 'config', 'usermod', 'ip route del', 'curl -u', 'grpconv', 'ifdown', 'groupadd', 'flushall', 'R([\\ ]+kill[\\ ]+)', 'R(^kill\\ )', 'reboot', 'rollout', 'slave_skip_errors', 'scale', 'R(^rm[\\ ]+)', 'cordon', 'incrby', 'systemctl hybrid-sleep', 'iptables -I', 'ip route add', 'systemctl kexec', 'route -6 add', 'xtrabackup --copy-back --target-dir', 'linsert', 'wget', 'ip6tables -I', 'rm * /', 'su', 'vi /etc/sudoers.d/*', 'mset', 'vim /etc/sudoers.d/*', 'iptables -A', 'history', 'grpck', 'ip6tables -A', 'rpop', 'flush', 'vigr', 'userdel', 'groupdel', 'systemctl poweroff', 'sysctl', 'curl -d', 'systemctl emergency', 'alter', 'R(.*mysql\\s+.*)', 'R(^\\s*systemctl\\s+start\\s+NetworkManager\\s*$)', 'R(^\\s*sudo\\s*history\\s+-d.*)', 'R(^\\s*sudo\\s*history\\s+-c.*)', 'R(^\\s*sudo\\s+systemctl\\s+emergency.*)', 'R(^\\s*ping.*169.254.*)', 'R(^\\s*(truncate|TRUNCATE).*$)', 'R((^\\s*sudo\\s+fsck.*)|(^\\s*fsck.*))', 'R(^\\s*(drop|DROP).*$)', 'R(^\\s*systemctl\\s+stop\\s+network\\s*$)', 'R((.*\\s+kill.*)|(^\\s*kill.*))', '/etc/init.d/network', 'R(^\\s*vi\\s+/etc/sudoers.d/.*)', 'R(^\\s*sudo\\s+systemctl\\s+halt.*)', 'R((^\\s*sudo\\s+KILL.*)|(^\\s*KILL.*))', 'R((^\\s*sudo\\s*sz\\s+)|(^\\s*sz\\s+))', 'R((^\\s*sudo\\s+reboot.*)|(^\\s*reboot.*))', 'R((^\\s*sudo\\s+groupdel.*)|(^\\s*groupdel.*))', 'R(^\\s*sudo\\s+screen\\s+-S\\s+test)', 'R((^\\s*sudo\\s+halt.*)|(^\\s*halt.*))', 'R((^\\s*sudo\\s*ssh\\s*)|(^\\s*ssh\\s*))', 'R(^curl\\s+-u.*$)', 'R(^\\s*ip\\s+route\\s+del.*)', 'R(^\\s*/bin/sh.*)', 'R(^\\s*sudo\\s+vim\\s+/etc/sudoers.d/.*)', 'R(^su$)', 'R(^.*manifests.*$)', 'R(^\\s*sudo\\s+systemctl\\s+stop\\s+network\\s*$)', 'R(^\\s*sudo\\s+systemctl\\s+restart\\s+Net.*M.*\\s*$)', 'R(^sudo\\s+curl\\s+-u.*$)', 'R((^\\s*sudo\\s+chmod.*)|(^\\s*chmod.*))', 'R(^\\s*sudo\\s+systemctl\\s+reset-failed.*)', 'R(.*\\s+stop.*$)', 'R((^\\s*sudo\\s+rm\\s+)|(^\\s*rm\\s+))', 'R((^\\s*sudo\\s+useradd.*)|(^\\s*useradd.*))', 'R(.*mongo\\s+.*)', 'R(^\\s*sudo\\s+sh\\s+-c\\s+.*)', 'R(^\\s*route\\s+del.*)', 'R(.*\\s+(delete|DELETE).*$)', 'R((^\\s*sudo\\s+ftp.*)|(^\\s*ftp.*))', 'R(.*psql\\s+.*)', 'R(^.*vim\\s+.*auditcmd.log)', 'R(^\\s*(flushall|FLUSHALL).*$)', 'R(^\\s*sudo\\s+iptables\\s+-D.*)', 'R(^\\s*(flushdb|FLUSHDB).*$)', 'R(^.*vi\\s+.*auditcmd.log)', 'R((^\\s*sudo\\s+xfs_repair.*)|(^\\s*xfs_repair.*))', 'R(^\\s*iptables\\s+-D.*)', 'R(^\\s*systemctl\\s+reset-failed.*)', 'R(.*redis-.*-a.*$)', 'R(^\\s*sudo\\s+/bin/sh.*)', 'R(^\\s*sudo\\s+ping.*169.254.*)', 'R(^\\s*sudo\\s+systemctl\\s+poweroff.*)', 'R(^\\s*(insert|INSERT).*$)', 'R((^\\s*sudo\\s+scp.*)|(^\\s*scp.*))', 'R((^\\s*sudo\\s+ansible.*)|(^\\s*ansible.*))', 'R(^\\s*systemctl\\s+start\\s+network\\s*$)', 'R(^\\s*sudo\\s+systemctl\\s+rescue.*)', 'R(^\\s*sudo\\s+iptables\\s+-I.*)', 'R(^\\s*systemctl\\s+emergency.*)', 'R((^\\s*sudo\\s+poweroff.*)|(^\\s*poweroff.*))', 'R(^\\s*systemctl\\s+poweroff.*)', 'R((^\\s*sudo\\s*su\\s*)|(^\\s*su\\s+))', 'R(^\\s*(alter|ALTER).*$)', 'R(^\\s*vim\\s+/etc/sudoers.d/.*)', 'R((^\\s*sudo\\s+shutdown.*)|(^\\s*shutdown.*))', 'R(^\\s*(update|UPDATE).*$)', 'R(^\\s*systemctl\\s+reboot.*)', 'R(^\\s*sudo\\s+systemctl\\s+restart\\s+network\\s*$)', 'R(^\\s*sudo\\s+ip\\s+route\\s+del.*)', 'R(^\\s*history\\s+-c.*)', 'R(^\\s*sudo\\s+systemctl\\s+reboot.*)', 'R(^\\s*iptables\\s+-I.*)', 'R(^\\s*systemctl\\s+rescue.*)', 'R((.*\\s+sftp.*)|(^\\s*sftp.*))', 'R(^\\s*sudo\\s+systemctl\\s+stop\\s+Net.*M.*\\s*$)', 'R(^\\s*systemctl\\s+stop\\s+NetworkManager\\s*$)', 'R((^\\s*sudo\\s+init\\s+.*)|(^\\s*init\\s+.*))', 'R(^\\s*sudo\\s+systemctl\\s+start\\s+Net.*M.*\\s*$)', 'R((^\\s*sudo\\s+sftp.*)|(^\\s*sftp.*))', 'R(^\\s*history\\s+-d.*)', 'R(^\\s*sudo\\s+systemctl\\s+start\\s+network\\s*$)', 'R((.*\\s+shutdown.*)|(^\\s*shutdown.*))', 'R(^\\s*systemctl\\s+restart\\s+network\\s*$)', 'R(^\\s*systemctl\\s+halt.*)', 'R(^\\s*sudo\\s+vi\\s+/etc/sudoers.d/.*)', 'R(^\\s*sudo\\s+route\\s+del.*)', 'R((^\\s*sudo\\s+userdel.*)|(^\\s*userdel.*))', 'R(^\\s*sh\\s+-c\\s+.*)', 'R(^\\s*hostnamectl\\s+set-hostname.*)', 'R((pgsql$)|(my$)|(sqlcmd$))', 'R(^\\s*sudo\\s+hostnamectl\\s+set-hostname.*)', 'R((^\\s*sudo\\s+passwd.*)|(^\\s*passwd.*))', 'R(^\\s*systemctl\\s+restart\\s+NetworkManager\\s*$)']
-script_content = "IyEvYmluL2Jhc2gKZm9yIGFwaXNlcnZlck5hbWUgaW4gJChzdWRvIC9hcHBzL2Jpbi9rdWJlY3RsIGdldCBwb2QgLW4ga3ViZS1zeXN0ZW0gfGdyZXAgYXBpc2VydmVyfGF3ayAne3ByaW50ICQxfScpCmRvCglTdGFydGVkX3RpbWU9JChzdWRvIC9hcHBzL2Jpbi9rdWJlY3RsIGRlc2NyaWJlIHBvZCAtbiBrdWJlLXN5c3RlbSAke2FwaXNlcnZlck5hbWV9IHxncmVwIFN0YXJ0ZWR8YXdrIC1GICJTdGFydGVkOiIgJ3twcmludCAkMn0nfHhhcmdzKQoJU3RhcnRlZF9kYXRlPSQoZGF0ZSAtZCBAJChkYXRlIC1kICIke1N0YXJ0ZWRfdGltZX0iICslcykgKyIlWS0lbS0lZCIpCglUb2RheV9kYXRlPSQoZGF0ZSArIiVZLSVtLSVkIikKCQoJaWYgW1sgIiRTdGFydGVkX2RhdGUiID09ICIkVG9kYXlfZGF0ZSIgXV07IHRoZW4KCQllY2hvICdBcGlTdGFydFN0YXR1c3tQb2ROYW1lPSdcIiR7YXBpc2VydmVyTmFtZX1cIicsVG9kYXlfZGF0ZT0nXCIke1RvZGF5X2RhdGV9XCInLFN0YXJ0ZWRfZGF0ZT0nXCIke1N0YXJ0ZWRfZGF0ZX1cIid9JyAxCgllbHNlCgkJZWNobyAnQXBpU3RhcnRTdGF0dXN7UG9kTmFtZT0nXCIke2FwaXNlcnZlck5hbWV9XCInLFRvZGF5X2RhdGU9J1wiJHtUb2RheV9kYXRlfVwiJyxTdGFydGVkX2RhdGU9J1wiJHtTdGFydGVkX2RhdGV9XCInfScgMAoJZmkKCmRvbmU="
+script_content = "IyEvYmluL3NoCmVjaG8gImRpc2tfdXNhZ2V7ZGlza19uYW1lPVwiL2RhdGFcIn0gMC44IgplY2hvICJiaWFuZ2VuZyIKZWNobyAidGVzdF8yMDI0MDYwNzAwMSIKZGVsZXRl"
 script_content = str(base64.b64decode(script_content), 'utf-8')
 script_content = script_content.split('\n')
 
 for content in script_content:
-    if content.startswith('#'):
-        continue
-
-    for ins in instructions:
-        print(ins)
-        if re.search("R(.*+manifest\s+.*)", content):
-            print(111)
+    print(content)
+    # if content.startswith('#'):
+    #     continue
+    #
+    # for ins in instructions:
+    #     print(ins)
+    #     if re.search("R(.*+manifest\s+.*)", content):
+    #         print(111)
