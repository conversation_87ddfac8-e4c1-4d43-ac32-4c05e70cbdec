Index: collect_process/config.py
===================================================================
diff --git a/collect_process/config.py b/collect_process/config.py
--- a/collect_process/config.py	
+++ b/collect_process/config.py	
@@ -18,10 +18,10 @@
 import json
 
 config = {
-    "Cookie": "blueking_language=zh-cn; bk_biz_id=205; bk_token=WOPYX_W2aaPxMwRjOeUlCZWF8HRyxBv5GncXd4SQq9Q; enterprise_monitor_csrftoken=u99GHDD8pOhxxdtCv3ksqBxmtXAkHsp2YaWoGgJQMYW3s04ezgwsuDQL8gRs30gJ; bk_monitorv3_sessionid=rywe3d0tlrstrc2695r0swwqzk00zzxc",
+    "Cookie": "bk_token=xKqGtU7mJ3SBkRzL-BSGqvhVQZ7Z55uj0ZbgWuNTNew; blueking_language=zh-cn; bk_biz_id=2; enterprise_monitor_csrftoken=lo9dSx0H7KXrymsdhDRHtKGyIntcsa5WmqxG4Xu7TwAZ33RMeUZI0DrGRrgJYwjn; bk_monitorv3_sessionid=k9jvudriiawtdwdaifofv5l9dawi28nm",
     "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
     "Content-Type": "application/json",
-    "X-Csrftoken": "XyQGSUDA4XIiv2ASrKqMOluOtDf7hYXYrzDoRxJir7nOqPbuvXCMSnNd8WwfDwOF",
+    "X-Csrftoken": "qnpZbjOKjuE2yQF77uWfyNhsazTeRBJYrpNsnJia5ghA3x4G4L4g5G2AjDGLnXXp",
     "base_url": "http://bkmonitor.ccops.cmecloud.cn/"
 }
 
@@ -47,16 +47,181 @@
               11843, 11844, 11845, 11590, 7621, 7625, 7627, 8140, 9675, 467, 6486, 11743, 6631, 12137, 11888, 7671,
               7672, 7673]
 
-du = [5774, 11829, 12020, 10104, 9708, 6293, 12030, 12031, 11824, 4158, 4992, 11279, 5775, 10138, 12035, 6467, 12034, 10841, 9689, 11803, 12131, 6486, 12022, 10392, 6435, 12032, 11806, 5794, 11276, 9481, 1296, 10331
-]
+# 9.20 重复采集
+du = [12645, 12634, 12651, 12642, 12187, 12652]
 
-bingxing = [9481, 11917, 4493, 6293, 12055, 7832, 9114, 11931, 6557, 8096, 8097, 5794, 6435, 10286, 7470, 7599, 9907, 8756, 8757, 7603, 11703, 11708, 12094, 11841, 11843, 11844, 11845, 11590, 7621, 7625, 7627, 8140, 9675, 467, 6486, 11743, 6631, 12137, 11888, 7671, 7672, 7673]
-
+# 9.20 不能并行
+bingxing = [6631, 6346, 5901, 11917, 11888, 9907, 467, 6557, 6207]
+# bingxing = [11877]
 more_5000 = [
-    12080
+    12306, 12312, 12307
 ]
 
-all = []
+deploy = [12592, 12598, 12322, 12595, 12306, 12324, 10680, 12605, 12590, 12312, 10783, 12614, 12307, 12612, 12620, 12316,
+          12600, 12618, 12611, 12596, 12616, 10937, 12621, 12627, 12619, 12325, 12603]
+
+upgrades = [
+    12286
+]
+
+
+all = [
+    {"bk_biz_id": 118, "name": "CU监控Mysql进程"},
+    {"bk_biz_id": 56, "name": "IDC_ceph_osd状态检查"},
+    {"bk_biz_id": 217, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 170, "name": "pfs_get_ops_metrics"},
+    {"bk_biz_id": 53, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 217, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 53, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 53, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 84, "name": "SQLServer_ALL_POD_STATUS"},
+    {"bk_biz_id": 170, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 17, "name": "EBS集群pool使用率监控"},
+    {"bk_biz_id": 49, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 123, "name": "系统时间异常告警"},
+    {"bk_biz_id": 123, "name": "linux主机僵尸进程采集"},
+    {"bk_biz_id": 49, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 62, "name": "deepwatch_pod_status"},
+    {"bk_biz_id": 49, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 123, "name": "磁盘分区读写状态检查"},
+    {"bk_biz_id": 49, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 27, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 48, "name": "磁盘分区读写状态检查"},
+    {"bk_biz_id": 48, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 83, "name": "redis_component_pod"},
+    {"bk_biz_id": 83, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 48, "name": "服务器网卡带宽利用率采集"},
+    {"bk_biz_id": 48, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 48, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 48, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 27, "name": "Base-NTP时间同步采集"},
+    {"bk_biz_id": 50, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 83, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 50, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 27, "name": "服务器网卡带宽利用率采集"},
+    {"bk_biz_id": 27, "name": "服务器网卡丢包率采集"},
+    {"bk_biz_id": 83, "name": "redis_etcd_status"},
+    {"bk_biz_id": 27, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 27, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 50, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 100, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 27, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 27, "name": "磁盘分区读写状态检查"},
+    {"bk_biz_id": 100, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 50, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 27, "name": "linux主机僵尸进程采集"},
+    {"bk_biz_id": 48, "name": "linux主机僵尸进程采集"},
+    {"bk_biz_id": 83, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 83, "name": "磁盘分区读写状态检查"},
+    {"bk_biz_id": 48, "name": "服务器网卡丢包率采集"},
+    {"bk_biz_id": 26, "name": "服务器网卡丢包率采集"},
+    {"bk_biz_id": 26, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 121, "name": "OAping值监控"},
+    {"bk_biz_id": 26, "name": "待消费消息数采集"},
+    {"bk_biz_id": 26, "name": "磁盘分区读写状态检查"},
+    {"bk_biz_id": 40, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 236, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 236, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 26, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 236, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 72, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 236, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 40, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 236, "name": "内外yum路径检查"},
+    {"bk_biz_id": 72, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 26, "name": "服务器网卡带宽利用率采集"},
+    {"bk_biz_id": 72, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 40, "name": "k8s-calico状态检测"},
+    {"bk_biz_id": 72, "name": "coredns域名解析检查"},
+    {"bk_biz_id": 26, "name": "linux主机僵尸进程采集"},
+    {"bk_biz_id": 26, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 72, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 40, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 21, "name": "测试域nova_conductor监控"},
+    {"bk_biz_id": 78, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 21, "name": "测试域k8s组件监控"},
+    {"bk_biz_id": 21, "name": "CNI_网元平面_软件_vsw_script_collect_testhost"},
+    {"bk_biz_id": 15, "name": "NFS-2049端口积压监控"},
+    {"bk_biz_id": 19, "name": "cni_api_server_mem"},
+    {"bk_biz_id": 19, "name": "cni_etcd_process_collect"},
+    {"bk_biz_id": 19, "name": "cni_nfv_collect"},
+    {"bk_biz_id": 78, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 78, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 19, "name": "省节点SDN-网络设备监控数据采集"},
+    {"bk_biz_id": 78, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 19, "name": "封堵IP退订监控_多资源池_v3"},
+    {"bk_biz_id": 19, "name": "cni_zk_collect"},
+    {"bk_biz_id": 19, "name": "cni_zk_exp_collect"},
+    {"bk_biz_id": 19, "name": "cni_zk_mem_collect"},
+    {"bk_biz_id": 19, "name": "sdn_bm_dhcp_status"},
+    {"bk_biz_id": 19, "name": "cni_etcd_script_collect"},
+    {"bk_biz_id": 15, "name": "NFS集群gluster服务quotad进程内存采集"},
+    {"bk_biz_id": 19, "name": "cni_mysql_collect"},
+    {"bk_biz_id": 19, "name": "cni_api_server_performance"},
+    {"bk_biz_id": 19, "name": "省节点SDN-ONC监控数据采集"},
+    {"bk_biz_id": 15, "name": "NFS集群brick进程CPU利用率采集-东莞容量型集群3"},
+    {"bk_biz_id": 57, "name": "云数据库MySQL-所用客户pod状态(排除2/3)"},
+    {"bk_biz_id": 57, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 57, "name": "HangYanSvcNcTest"},
+    {"bk_biz_id": 57, "name": "磁盘分区读写状态检查"},
+    {"bk_biz_id": 57, "name": "系统进程文件打开数量采集"},
+    {"bk_biz_id": 57, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 57, "name": "MySQL_Kubepod_Exception"},
+    {"bk_biz_id": 57, "name": "all_proxy_check"},
+    {"bk_biz_id": 57, "name": "mysql_ping_S3"},
+    {"bk_biz_id": 57, "name": "云MySQL杭研磁盘空间使用率采集"},
+    {"bk_biz_id": 57, "name": "k8s-calico状态检测"},
+    {"bk_biz_id": 271, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 299, "name": "EBS集群未固化路由检查-V4"},
+    {"bk_biz_id": 271, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 299, "name": "EBS系统进程文件打开数量异常"},
+    {"bk_biz_id": 299, "name": "EBS_系统时间异常"},
+    {"bk_biz_id": 299, "name": "EBS集群未固化路由检查-V6"},
+    {"bk_biz_id": 271, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 232, "name": "对象存储慢盘监控自定义上报"},
+    {"bk_biz_id": 234, "name": "ONEST-pool usage采集"},
+    {"bk_biz_id": 16, "name": "EOS_G3集群入流量突降采集"},
+    {"bk_biz_id": 232, "name": "ONEST-G3list基线采集"},
+    {"bk_biz_id": 234, "name": "ONEST-rgw状态码异常采集"},
+    {"bk_biz_id": 234, "name": "ONEST-curl-vip"},
+    {"bk_biz_id": 235, "name": "ONEST-G3list基线采集"},
+    {"bk_biz_id": 233, "name": "ONEST-pool usage采集"},
+    {"bk_biz_id": 16, "name": "对象存储G3list基线采集"},
+    {"bk_biz_id": 235, "name": "ONEST-pool usage采集"},
+    {"bk_biz_id": 233, "name": "ONEST-tcptimeout异常采集"},
+    {"bk_biz_id": 16, "name": "ONEST_curl_rgw采集"},
+    {"bk_biz_id": 235, "name": "EOS_G3集群入流量突降采集"},
+    {"bk_biz_id": 235, "name": "ONEST-系统时间异常"},
+    {"bk_biz_id": 232, "name": "ONEST-rgw状态码异常采集"},
+    {"bk_biz_id": 235, "name": "ONEST-rgw状态码异常采集"},
+    {"bk_biz_id": 232, "name": "ONEST-pool usage采集"},
+    {"bk_biz_id": 16, "name": "ONEST-pool usage采集"},
+    {"bk_biz_id": 233, "name": "ONEST-rgw状态码异常采集"},
+    {"bk_biz_id": 234, "name": "ONEST-rgw初始化异常采集"},
+    {"bk_biz_id": 205, "name": "EBS_性能基线采集"},
+    {"bk_biz_id": 205, "name": "ONEST-mon status采集"},
+    {"bk_biz_id": 205, "name": "ECSO_SDN-ONC监控数据采集"},
+    {"bk_biz_id": 205, "name": "ONEST-集群状态采集"},
+    {"bk_biz_id": 13, "name": "高端云cinder后端存储状态检查"},
+    {"bk_biz_id": 205, "name": "ONEST-slow request采集"},
+    {"bk_biz_id": 13, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 205, "name": "EC 时间同步"},
+    {"bk_biz_id": 13, "name": "未固化路由检查-V6"},
+    {"bk_biz_id": 40, "name": "云空间m_bcmysqlms_available采集"},
+    {"bk_biz_id": 57, "name": "bcmysql主从状态监控"},
+    {"bk_biz_id": 62, "name": "bcmysql"},
+    {"bk_biz_id": 228, "name": "EC bcrdb集群状态采集base"},
+    {"bk_biz_id": 57, "name": "Base-bcmysql8.0监控采集"},
+    {"bk_biz_id": 205, "name": "Base-bcmysql8.0监控采集"},
+    {"bk_biz_id": 40, "name": "云空间订购跨AZ数据库监控"},
+    {"bk_biz_id": 57, "name": "云数据库MySQL-VIP拨测"},
+    {"bk_biz_id": 16, "name": "硬盘await时延监控_V2"},
+    {"bk_biz_id": 205, "name": "NTP时间同步采集"},
+    {"bk_biz_id": 205, "name": "未固化路由检查-V4"},
+    {"bk_biz_id": 234, "name": "服务器硬盘故障监控采集"},
+    {"bk_biz_id": 205, "name": "应用-NTP时间同步采集"},
+    {"bk_biz_id": 16, "name": "慢盘监控自定义上报"}
+]
 
 encrypts = [
     {"bk_biz_id": 368, "name": "Base-bcmysql8.0监控采集"},
Index: collect_process/encrypt.py
===================================================================
diff --git a/collect_process/encrypt.py b/collect_process/encrypt.py
--- a/collect_process/encrypt.py	
+++ b/collect_process/encrypt.py	
@@ -58,5 +58,5 @@
 
 cipher = AESCipher(key)
 
-a = cipher.encrypt("xxx")
+a = cipher.encrypt("A8@U8VUVT*hkxA3e")
 print(a)
Index: collect_process/deploy_collect.py
===================================================================
diff --git a/collect_process/deploy_collect.py b/collect_process/deploy_collect.py
--- a/collect_process/deploy_collect.py	
+++ b/collect_process/deploy_collect.py	
@@ -6,7 +6,7 @@
 import requests
 
 # not_deploy部分为用户主机数量超过5000，需要自行下发的采集id
-from config import config, not_deploy, more_5000, bingxing
+from config import config, not_deploy, more_5000, bingxing, du, upgrades
 
 parser = argparse.ArgumentParser()
 
@@ -56,7 +56,7 @@
     except Exception as e:
         print("采集下发失败", e)
         ferror.write(
-            "采集下发失败" + "\t" + str(data["bk_biz_id"]) + "\t" + data["name"] + "\t" + data["plugin_id"] + "\t" +
+            "采集下发失败" + "\t" + str(data["bk_biz_id"]) + "\t" + "\t" + data["name"] + "\t" + data["plugin_id"] + "\t" +
             "调用下发接口失败" + "\n")
 
 
@@ -69,25 +69,26 @@
     fnew_id = open("./need_select/new_select_sql.json", "r")
     new_id = json.load(fnew_id)
     fyingshe = open("./old_new.txt", "w")
+    count = 0
     for filename in filenames:
+        count += 1
+        print(count)
         f = open(filename, "r")
         data = json.load(f)
         new_config = new_id[filename.split("/")[-1]]
 
-        # if data["id"] in not_deploy:
-        #     # 第一轮，过滤掉重复，超5000，和不能并行的
-        #     # 第三轮，密码错误的
-        #     print("====================================================", data["id"])
-            # continue
-
-        # if data["id"] not in more_5000:
-        #     # 第二轮，跑超过5000的数据
-        #     continue
-
-        if data["id"] not in bingxing:
+        if data["id"] in du:
+            print("chongfu..........", data["id"])
+            continue
+        #
+        # if data["id"] in bingxing:
+        #     print("bingxing.............")
+        #
+        if data["id"] not in upgrades:
             continue
+        print("upgrade.......", data["id"])
 
-        print("running..........", data["id"])
+        # print("running..........", data["id"])
 
         # 有重复id，记录
         if type(new_config["new_collect_id"]) == list:
@@ -98,19 +99,24 @@
                 ferror.write("新建采集" + "\n")
                 fyingshe.write(str(data["id"]) + "\t")
                 # 处理不能并行
-                # data["target_nodes"] = []
+                if data["id"] in bingxing:
+                    data["target_nodes"] = []
                 # 新增采集配置,   去掉id
                 del data["id"]
                 del data["subscription_id"]
+                print(data)
                 save_collect_config(data, ferror, fyingshe)
             else:
                 # 编辑采集配置,采集id修改为已查询到的采集id
                 ferror.write("编辑采集" + "\n")
                 fyingshe.write(str(data["id"]) + "\t")
-                data["id"] = new_config["new_collect_id"]
                 # 处理不能并行
-                # data["target_nodes"] = []
+                if data["id"] in bingxing:
+                    data["target_nodes"] = []
+                # 编辑使用新的
+                data["id"] = new_config["new_collect_id"]
                 del data["subscription_id"]
+                print(data)
                 save_collect_config(data, ferror, fyingshe)
 
 
