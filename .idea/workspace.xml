<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2f5c8b63-eb87-4907-a6ce-d91afb4c76a4" name="Changes" comment="主机监控开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-34628&#10;市场项目编号（名称）:">
      <change afterPath="$PROJECT_DIR$/bkmonitor/packages/monitor_web/base_monitor/resources/test_collecting_resources.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/bkmonitor/config/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/bkmonitor/config/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/bkmonitor/packages/utils/query_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/bkmonitor/packages/utils/query_data.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="DjangoConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)">
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="release_bcops_v5.13.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/bkmonitor/alarm_backends/core/cache/strategy.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/api/node_man/default.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/bkmonitor/commons/tools.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/core/drf_resource/base.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/frontend_resources.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/views.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/export_import/views.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/resources.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/views.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/strategies/metric_list_cache.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2akkSZIknStOwEffkPOvBFbPYCJ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Git.Branch.Popup.ShowAllRemotes": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "/Users/<USER>/workspace/bk-monitor",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "shared-indexes",
    "ts.external.directory.path": "/Applications/PyCharm.app/Contents/plugins/JavaScriptLanguage/jsLanguageServicesImpl/external"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/collect_process/collect/collect_unzip/collect_test" />
      <recent name="$PROJECT_DIR$/collect_process/need_select/dist" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/collect_process/history" />
      <recent name="$PROJECT_DIR$/collect_process/collect/collect_zip/2" />
      <recent name="$PROJECT_DIR$/collect_process/collect/collect_zip/3" />
      <recent name="$PROJECT_DIR$/collect_process/collect/collect_config_directory" />
      <recent name="$PROJECT_DIR$/collect_process/collect/collect_unzip" />
    </key>
  </component>
  <component name="RunManager" selected="Python.manage">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="/usr/local/bin/python3.6" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="collect_process" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/collect_process" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/collect_process/collect_process.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="duibi" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="/usr/local/bin/python3.6" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/collect_process" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/collect_process/duibi.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="encrypt" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="/usr/local/bin/python3.6" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/collect_process" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/collect_process/encrypt.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="manage" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
        <env name="APP_CODE" value="bk_monitorv3" />
        <env name="APP_TOKEN" value="cb31403e-055c-4c12-a9af-df5a76e4a561" />
        <env name="BK_BCS_CLUSTER_SOURCE" value="cluster-manager" />
        <env name="BK_BCS_URL" value="http://bcs.ccops.bcopstest.com/" />
        <env name="BK_CC_SITE_URL" value="http://cmdb.ccops.bcopstest.com/" />
        <env name="BK_COMPONENT_API_URL" value="http://bkapi.ccops.bcopstest.com" />
        <env name="BK_DOCS_SITE_URL" value="http://apps.ccops.bcopstest.com/bk--docs--center/" />
        <env name="BK_FTA_ES7_REST_PORT" value="31905" />
        <env name="BK_GSE_ZK_HOST" value="bk-gse-zookeeper-headless" />
        <env name="BK_GSE_ZK_PORT" value="2181" />
        <env name="BK_HOME" value="$PROJECT_DIR$/bkmonitor" />
        <env name="BK_IAM_MIGRATION_JSON_PATH" value="iam/" />
        <env name="BK_IAM_SITE_URL" value="http://bkiam.ccops.bcopstest.com" />
        <env name="BK_IAM_SYSTEM_ID" value="bk_monitorv3" />
        <env name="BK_IAM_V3_INNER_HOST" value="http://bkiam-api.ccops.bcopstest.com" />
        <env name="BK_ITSM_HOST" value="http://apps.ccops.bcopstest.com/bk--itsm/" />
        <env name="BK_JOB_SITE_URL" value="http://job.ccops.bcopstest.com/" />
        <env name="BK_LOG_SEARCH_SITE_URL" value="http://bklog.ccops.bcopstest.com/" />
        <env name="BK_MONITOR_APP_CODE" value="bk_monitorv3" />
        <env name="BK_MONITOR_APP_SECRET" value="fb95d397-28a7-4f81-a4a6-75ad0d32d215" />
        <env name="BK_MONITOR_ES7_HOST" value="*************" />
        <env name="BK_MONITOR_ES7_PASSWORD" value="PCI3T6t_D08F" />
        <env name="BK_MONITOR_ES7_REST_PORT" value="31905" />
        <env name="BK_MONITOR_ES7_USER" value="elastic" />
        <env name="BK_MONITOR_HOST" value="http://bkmonitor.ccops.bcopstest.com/" />
        <env name="BK_MONITOR_MYSQL_HOST" value="127.0.0.1" />
        <env name="BK_MONITOR_MYSQL_NAME" value="bk_monitor" />
        <env name="BK_MONITOR_MYSQL_PASSWORD" value="1234" />
        <env name="BK_MONITOR_MYSQL_PORT" value="3306" />
        <env name="BK_MONITOR_MYSQL_USER" value="root" />
        <env name="BK_MONITOR_RABBITMQ_HOST" value="*************" />
        <env name="BK_MONITOR_RABBITMQ_PASSWORD" value="9Yp_MO6nI3toR" />
        <env name="BK_MONITOR_RABBITMQ_PORT" value="5672" />
        <env name="BK_MONITOR_RABBITMQ_USERNAME" value="bkmonitor" />
        <env name="BK_MONITOR_RABBITMQ_VHOST" value="bkmonitor" />
        <env name="BK_MONITOR_UNIFY_QUERY_HOST" value="unify-query.paas.cmsstest.com" />
        <env name="BK_MONITOR_UNIFY_QUERY_PORT" value="80" />
        <env name="BK_NODEMAN_SITE_URL" value="http://bknodeman.ccops.bcopstest.com" />
        <env name="BK_PAAS_HOST" value="http://paas.ccops.bcopstest.com" />
        <env name="BK_SOPS_URL" value="http://apps.ccops.bcopstest.com/bk--sops/" />
        <env name="BK_SSM_HOST" value="http://bkssm.ccops.bcopstest.com" />
        <env name="BK_SSM_PORT" value="80" />
        <env name="BKAPP_BCS_API_GATEWAY_HOST" value="bkapi.ccops.bcopstest.com" />
        <env name="BKAPP_BCS_API_GATEWAY_PORT" value="80" />
        <env name="BKAPP_BCS_API_GATEWAY_SCHEMA" value="http" />
        <env name="BKAPP_BCS_API_GATEWAY_TOKEN" value="pPgJcfMk0d3HYFuJB1HDvUTjlBX89S67" />
        <env name="BKAPP_BCS_CC_API_URL" value="http://bcs.ccops.bcopstest.com:5000" />
        <env name="BKAPP_DEPLOY_PLATFORM" value="community" />
        <env name="BKAPP_FTA_ES7_HOST" value="*************" />
        <env name="BKAPP_FTA_ES7_PASSWORD" value="PCI3T6t_D08F" />
        <env name="BKAPP_FTA_ES7_USER" value="elastic" />
        <env name="BKAPP_GRAFANA_URL" value="http://bk-monitor-grafana:3000" />
        <env name="BKAPP_IAM_RESOURCE_API_HOST" value="http://bk-monitor-web/" />
        <env name="BKAPP_LOG_LEVEL" value="DEBUG" />
        <env name="BKAPP_SAAS_DB_HOST" value="************" />
        <env name="BKAPP_SAAS_DB_PASSWORD" value="VL0aYOWYjV5b" />
        <env name="BKAPP_SAAS_DB_PORT" value="31422" />
        <env name="BKAPP_SAAS_DB_USER" value="paas" />
        <env name="BKREPO_BUCKET" value="bkmonitor" />
        <env name="BKREPO_ENDPOINT_URL" value="http://bkrepo.paas.cmsstest.com" />
        <env name="BKREPO_PASSWORD" value="bkmonitor" />
        <env name="BKREPO_PROJECT" value="blueking" />
        <env name="BKREPO_USERNAME" value="bkmonitor" />
        <env name="CMSS_4A_BASE_URL" value="http://**************:9090" />
        <env name="CMSS_4A_INSTRUCT_URL" value="/api/jk/v1/jkscene/getCmdBySceneName" />
        <env name="CMSS_4A_SCENE_NAME" value="ce2" />
        <env name="CMSS_4A_SM4_KEY" value="ultrapower@20214" />
        <env name="CMSS_4A_TENANT_ID" value="10530ec8d0414f7481de18606adac2b1" />
        <env name="CMSS_4A_TOKRN_URL" value="/api/user/sti/getToken4Http" />
        <env name="CMSS_INSTRUCT_SOURCE" value="4A" />
        <env name="DB_NAME" value="bk_monitorv3" />
        <env name="DJANGO_CONF_MODULE" value="conf.web.production.community" />
        <env name="DJANGO_SETTINGS_MODULE" value="settings" />
        <env name="OPENSSL_CONF" value="/dev/null" />
        <env name="RABBITMQ_HOST" value="127.0.0.1" />
        <env name="RABBITMQ_PASSWORD" value="admin" />
        <env name="RABBITMQ_PORT" value="5672" />
        <env name="RABBITMQ_USER" value="admin" />
        <env name="RABBITMQ_VHOST" value="/" />
        <env name="UPLOAD_PLUGIN_VIA_COS" value="true" />
        <env name="USE_BKREPO" value="false" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/bkmonitor" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="false" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/bkmonitor/manage.py" />
      <option name="PARAMETERS" value="runserver 0.0.0.0:80" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="python manage.py run_access -s access --access-type=data --min-interval 30" type="PythonConfigurationType" factoryName="Python">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="$PROJECT_DIR$/monitor_venv/bin/python" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/bkmonitor/alarm_backends" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="manage.py" />
      <option name="PARAMETERS" value="run_access -s access --access-type=data --min-interval 30" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="status_data" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="/usr/local/bin/python3.6" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/collect_process/status" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/collect_process/status/status_data.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="upgrade" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="/usr/local/bin/python3.6" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/collect_process" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/collect_process/upgrade.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.DjangoServer" factoryName="Django server">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/bkmonitor" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="launchJavascriptDebuger" value="false" />
      <option name="port" value="8000" />
      <option name="host" value="0.0.0.0" />
      <option name="additionalOptions" value="" />
      <option name="browserUrl" value="" />
      <option name="runTestServer" value="false" />
      <option name="runNoReload" value="false" />
      <option name="useCustomRunCommand" value="false" />
      <option name="customRunCommand" value="" />
      <method v="2" />
    </configuration>
    <configuration name="bk-monitor" type="Python.DjangoServer" factoryName="Django server">
      <module name="bk-monitor" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/bkmonitor" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="launchJavascriptDebuger" value="false" />
      <option name="port" value="8000" />
      <option name="host" value="0.0.0.0" />
      <option name="additionalOptions" value="" />
      <option name="browserUrl" value="" />
      <option name="runTestServer" value="false" />
      <option name="runNoReload" value="false" />
      <option name="useCustomRunCommand" value="false" />
      <option name="customRunCommand" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Django Server.bk-monitor" />
      <item itemvalue="Python.python manage.py run_access -s access --access-type=data --min-interval 30" />
      <item itemvalue="Python.manage" />
      <item itemvalue="Python.encrypt" />
      <item itemvalue="Python.upgrade" />
      <item itemvalue="Python.duibi" />
      <item itemvalue="Python.status_data" />
      <item itemvalue="Python.collect_process" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.collect_process" />
        <item itemvalue="Python.status_data" />
        <item itemvalue="Python.duibi" />
        <item itemvalue="Python.upgrade" />
        <item itemvalue="Python.encrypt" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2f5c8b63-eb87-4907-a6ce-d91afb4c76a4" name="Changes" comment="" />
      <created>1704869968072</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1704869968072</updated>
      <workItem from="1704869984840" duration="4958000" />
      <workItem from="1704935355212" duration="22609000" />
      <workItem from="1705023045871" duration="10202000" />
      <workItem from="1705048545348" duration="4153000" />
      <workItem from="1705282152361" duration="22240000" />
      <workItem from="1705367470454" duration="25042000" />
      <workItem from="1705454906838" duration="13266000" />
      <workItem from="1705475714555" duration="43000" />
      <workItem from="1705476274519" duration="1690000" />
      <workItem from="1705478401386" duration="6006000" />
      <workItem from="1705539910006" duration="25828000" />
      <workItem from="1705627700178" duration="19752000" />
      <workItem from="1705885417037" duration="20237000" />
      <workItem from="1705972453253" duration="10781000" />
      <workItem from="1706059085613" duration="14970000" />
      <workItem from="1706144772111" duration="4107000" />
      <workItem from="1706231304521" duration="15305000" />
      <workItem from="1706576902257" duration="21616000" />
      <workItem from="1706663305536" duration="18951000" />
      <workItem from="1706749803379" duration="25240000" />
      <workItem from="1706837403125" duration="23247000" />
      <workItem from="1707008865461" duration="24903000" />
      <workItem from="1707095154073" duration="22175000" />
      <workItem from="1707181944821" duration="22737000" />
      <workItem from="1707268454797" duration="14495000" />
      <workItem from="1707354335902" duration="5442000" />
      <workItem from="1708477920425" duration="12054000" />
      <workItem from="1708498617658" duration="8541000" />
      <workItem from="1708564282180" duration="21125000" />
      <workItem from="1708649820138" duration="13453000" />
      <workItem from="1708909485121" duration="24207000" />
      <workItem from="1708996171302" duration="8448000" />
      <workItem from="1709084074146" duration="19729000" />
      <workItem from="1709169085049" duration="17280000" />
      <workItem from="1709257253567" duration="18837000" />
      <workItem from="1709344912327" duration="8267000" />
      <workItem from="1709515594258" duration="8747000" />
      <workItem from="1709532625305" duration="655000" />
      <workItem from="1709533287772" duration="12819000" />
      <workItem from="1709601240308" duration="24499000" />
      <workItem from="1709687173842" duration="19940000" />
      <workItem from="1709773747659" duration="14666000" />
      <workItem from="1709860204048" duration="5837000" />
      <workItem from="1710119134764" duration="16157000" />
      <workItem from="1710206028054" duration="19576000" />
      <workItem from="1710292307917" duration="22613000" />
      <workItem from="1710465036420" duration="21118000" />
      <workItem from="1710555621819" duration="12970000" />
      <workItem from="1710725004922" duration="17969000" />
      <workItem from="1710810865238" duration="18446000" />
      <workItem from="1710897041807" duration="15413000" />
      <workItem from="1710983479090" duration="19833000" />
      <workItem from="1711069439714" duration="14618000" />
      <workItem from="1711329298844" duration="9684000" />
      <workItem from="1711507066998" duration="10121000" />
      <workItem from="1711588193032" duration="5426000" />
      <workItem from="1711597486079" duration="2195000" />
      <workItem from="1711674438653" duration="10186000" />
      <workItem from="1711933609022" duration="20425000" />
      <workItem from="1712020312843" duration="12356000" />
      <workItem from="1712106190822" duration="253000" />
      <workItem from="1712124100818" duration="552000" />
      <workItem from="1712125397428" duration="815000" />
      <workItem from="1712126502406" duration="8148000" />
      <workItem from="1713747466094" duration="19242000" />
      <workItem from="1713834324732" duration="14378000" />
      <workItem from="1713920620547" duration="7269000" />
      <workItem from="1714034601457" duration="1742000" />
      <workItem from="1714095495865" duration="3378000" />
      <workItem from="1714285129595" duration="7555000" />
      <workItem from="1714295302603" duration="2379000" />
      <workItem from="1714352683552" duration="17486000" />
      <workItem from="1714439295721" duration="6824000" />
      <workItem from="1714957280446" duration="18774000" />
      <workItem from="1715044125995" duration="5937000" />
      <workItem from="1715052861188" duration="363000" />
      <workItem from="1715130516000" duration="19190000" />
      <workItem from="1715217010115" duration="598000" />
      <workItem from="1715301749104" duration="2708000" />
      <workItem from="1715389563067" duration="2164000" />
      <workItem from="1715563034742" duration="8995000" />
      <workItem from="1715580958937" duration="11041000" />
      <workItem from="1715648592947" duration="1284000" />
      <workItem from="1715653935625" duration="16361000" />
      <workItem from="1715735035602" duration="3747000" />
      <workItem from="1715783133806" duration="1528000" />
      <workItem from="1715786435444" duration="1490000" />
      <workItem from="1715843960852" duration="3736000" />
      <workItem from="1715908113462" duration="8095000" />
      <workItem from="1716167261958" duration="15011000" />
      <workItem from="1716192471103" duration="4611000" />
      <workItem from="1716253680026" duration="13339000" />
      <workItem from="1716340978555" duration="10492000" />
      <workItem from="1716426148181" duration="5030000" />
      <workItem from="1716511694290" duration="3415000" />
      <workItem from="1716771791773" duration="9128000" />
      <workItem from="1716858331740" duration="9709000" />
      <workItem from="1716879993237" duration="6597000" />
      <workItem from="1716944403424" duration="5182000" />
      <workItem from="1716962079388" duration="32000" />
      <workItem from="1716962115854" duration="1988000" />
      <workItem from="1716971797661" duration="2206000" />
      <workItem from="1717030915128" duration="9489000" />
      <workItem from="1717117644728" duration="7410000" />
      <workItem from="1717378409225" duration="1098000" />
      <workItem from="1717384192700" duration="6460000" />
      <workItem from="1717549403628" duration="9544000" />
      <workItem from="1717637055079" duration="9245000" />
      <workItem from="1717724924199" duration="5013000" />
      <workItem from="1718154209829" duration="23468000" />
      <workItem from="1718240848255" duration="22993000" />
      <workItem from="1718326732063" duration="16024000" />
      <workItem from="1718587192521" duration="15019000" />
      <workItem from="1718672640376" duration="14084000" />
      <workItem from="1718759106572" duration="18481000" />
      <workItem from="1718845808194" duration="16494000" />
      <workItem from="1718931241697" duration="12712000" />
      <workItem from="1719192374402" duration="7921000" />
      <workItem from="1719277081934" duration="17084000" />
      <workItem from="1719364202943" duration="5235000" />
      <workItem from="1719450226242" duration="21804000" />
      <workItem from="1719535908766" duration="24395000" />
      <workItem from="1719622867069" duration="12464000" />
      <workItem from="1719797067835" duration="20824000" />
      <workItem from="1719881900127" duration="21968000" />
      <workItem from="1719968359933" duration="16931000" />
      <workItem from="1720056433003" duration="10346000" />
      <workItem from="1720075622485" duration="11005000" />
      <workItem from="1720141974328" duration="26198000" />
      <workItem from="1720400933120" duration="8853000" />
      <workItem from="1720420366173" duration="9388000" />
      <workItem from="1720487466563" duration="17424000" />
      <workItem from="1720574456803" duration="13377000" />
      <workItem from="1720660104372" duration="18413000" />
      <workItem from="1721006728110" duration="11958000" />
      <workItem from="1721092410581" duration="17458000" />
      <workItem from="1721178602047" duration="21119000" />
      <workItem from="1721264576296" duration="7995000" />
      <workItem from="1721283697285" duration="6340000" />
      <workItem from="1721350728919" duration="14045000" />
      <workItem from="1721610129676" duration="21639000" />
      <workItem from="1721696936510" duration="28389000" />
      <workItem from="1721869765187" duration="5502000" />
      <workItem from="1721957531641" duration="18289000" />
      <workItem from="1722214773284" duration="16199000" />
      <workItem from="1722301846700" duration="17920000" />
      <workItem from="1722390203516" duration="15407000" />
      <workItem from="1722474410963" duration="13367000" />
      <workItem from="1722562809744" duration="12000" />
      <workItem from="1722562980232" duration="13696000" />
      <workItem from="1722819985431" duration="2671000" />
      <workItem from="1722824158291" duration="230000" />
      <workItem from="1722824392451" duration="16115000" />
      <workItem from="1722908970882" duration="11025000" />
      <workItem from="1723000421591" duration="8840000" />
      <workItem from="1723079033998" duration="15247000" />
      <workItem from="1723165518418" duration="863000" />
      <workItem from="1723171680614" duration="1773000" />
      <workItem from="1723424599255" duration="4626000" />
      <workItem from="1723510723289" duration="7161000" />
      <workItem from="1723603352879" duration="3910000" />
      <workItem from="1723683996061" duration="616000" />
      <workItem from="1723772123357" duration="3525000" />
      <workItem from="1724116199262" duration="5466000" />
      <workItem from="1724205579831" duration="602000" />
      <workItem from="1724207242367" duration="598000" />
      <workItem from="1724288777170" duration="3175000" />
      <workItem from="1724393984932" duration="2324000" />
      <workItem from="1724639706086" duration="2938000" />
      <workItem from="1724663874340" duration="2469000" />
      <workItem from="1724719835925" duration="1137000" />
      <workItem from="1724728358197" duration="3099000" />
      <workItem from="1724807179407" duration="11529000" />
      <workItem from="1724893583312" duration="8198000" />
      <workItem from="1724980148257" duration="3596000" />
      <workItem from="1725241563156" duration="14451000" />
      <workItem from="1725328927475" duration="2821000" />
      <workItem from="1725411560062" duration="15784000" />
      <workItem from="1725498592638" duration="20661000" />
      <workItem from="1725584485641" duration="1287000" />
      <workItem from="1725585939762" duration="8951000" />
      <workItem from="1725866902011" duration="1190000" />
      <workItem from="1725930231837" duration="10553000" />
      <workItem from="1725962486040" duration="813000" />
      <workItem from="1726017771147" duration="7752000" />
      <workItem from="1726044845210" duration="3672000" />
      <workItem from="1726103455357" duration="14315000" />
      <workItem from="1726189503951" duration="20264000" />
      <workItem from="1726275811301" duration="12808000" />
      <workItem from="1726295244941" duration="7162000" />
      <workItem from="1726621645526" duration="9007000" />
      <workItem from="1726707592486" duration="7476000" />
      <workItem from="1726727661739" duration="6834000" />
      <workItem from="1726736065443" duration="4556000" />
      <workItem from="1726793917854" duration="21339000" />
      <workItem from="1727053271865" duration="14962000" />
      <workItem from="1727078651607" duration="5812000" />
      <workItem from="1727139979817" duration="3657000" />
      <workItem from="1727161289192" duration="2760000" />
      <workItem from="1727225251338" duration="17801000" />
      <workItem from="1727312062416" duration="6044000" />
      <workItem from="1727398127954" duration="1607000" />
      <workItem from="1727571457363" duration="629000" />
      <workItem from="1727576210249" duration="2271000" />
      <workItem from="1727657950500" duration="13776000" />
      <workItem from="1728349198132" duration="295000" />
      <workItem from="1728349563814" duration="3912000" />
      <workItem from="1728355393585" duration="3795000" />
      <workItem from="1728434598517" duration="8402000" />
      <workItem from="1728524167822" duration="2396000" />
      <workItem from="1728549573800" duration="4722000" />
      <workItem from="1728696030502" duration="11769000" />
      <workItem from="1728867625715" duration="8499000" />
      <workItem from="1728954076938" duration="16328000" />
      <workItem from="1729040094214" duration="7218000" />
      <workItem from="1729127145527" duration="13225000" />
      <workItem from="1729212391530" duration="10044000" />
      <workItem from="1729255486833" duration="760000" />
      <workItem from="1729387847049" duration="40800000" />
      <workItem from="1729559094230" duration="9150000" />
      <workItem from="1729606819085" duration="6009000" />
      <workItem from="1729665352766" duration="7714000" />
      <workItem from="1729732042580" duration="6560000" />
      <workItem from="1729828337965" duration="2209000" />
      <workItem from="1729924046404" duration="1515000" />
      <workItem from="1730081067022" duration="19500000" />
      <workItem from="1730164286540" duration="16141000" />
      <workItem from="1730249704669" duration="12088000" />
      <workItem from="1730335936472" duration="16393000" />
      <workItem from="1730423510717" duration="645000" />
      <workItem from="1730425629168" duration="3582000" />
      <workItem from="1730681784830" duration="5813000" />
      <workItem from="1730700252422" duration="8198000" />
      <workItem from="1730854617884" duration="2692000" />
      <workItem from="1730862342063" duration="4247000" />
      <workItem from="1730958939611" duration="613000" />
      <workItem from="1730971914357" duration="1936000" />
      <workItem from="1731028502519" duration="2216000" />
      <workItem from="1731057604902" duration="637000" />
      <workItem from="1731287351608" duration="22043000" />
      <workItem from="1731373641023" duration="10047000" />
      <workItem from="1731395691566" duration="616000" />
      <workItem from="1731399383250" duration="1225000" />
      <workItem from="1731460871589" duration="4817000" />
      <workItem from="1731467330140" duration="96000" />
      <workItem from="1731551209859" duration="3465000" />
      <workItem from="1731569245622" duration="2443000" />
      <workItem from="1731631534958" duration="1514000" />
      <workItem from="1731634442791" duration="9122000" />
      <workItem from="1731655993155" duration="328000" />
      <workItem from="1731892059525" duration="4132000" />
      <workItem from="1731978213120" duration="16246000" />
      <workItem from="1732064783103" duration="8084000" />
      <workItem from="1732158469399" duration="1730000" />
      <workItem from="1732179728769" duration="604000" />
      <workItem from="1732238683918" duration="8479000" />
      <workItem from="1732267637199" duration="1804000" />
      <workItem from="1732498394564" duration="5608000" />
      <workItem from="1732582128312" duration="11101000" />
      <workItem from="1732669032443" duration="8790000" />
      <workItem from="1732778409251" duration="935000" />
      <workItem from="1732842827304" duration="599000" />
      <workItem from="1733101535997" duration="1895000" />
      <workItem from="1733187380552" duration="5223000" />
      <workItem from="1733206999353" duration="11600000" />
      <workItem from="1733273721549" duration="20156000" />
      <workItem from="1733360422755" duration="1439000" />
      <workItem from="1733881597090" duration="16504000" />
      <workItem from="1733964782993" duration="8766000" />
      <workItem from="1734055255475" duration="816000" />
      <workItem from="1734311469163" duration="603000" />
      <workItem from="1734314228753" duration="639000" />
      <workItem from="1734587230629" duration="557000" />
      <workItem from="1735551995734" duration="790000" />
      <workItem from="1735638366215" duration="54000" />
      <workItem from="1736149137657" duration="540000" />
      <workItem from="1736149704102" duration="31000" />
      <workItem from="1736826011355" duration="3711000" />
      <workItem from="1736911308467" duration="11759000" />
      <workItem from="1736988853030" duration="4420000" />
      <workItem from="1737083227125" duration="467000" />
      <workItem from="1737440362824" duration="7300000" />
      <workItem from="1737515213458" duration="3756000" />
      <workItem from="1737532964942" duration="1239000" />
      <workItem from="1737593084853" duration="2326000" />
      <workItem from="1739155174905" duration="2041000" />
      <workItem from="1743488112402" duration="3400000" />
      <workItem from="1745303402603" duration="3217000" />
      <workItem from="1745825625417" duration="1005000" />
      <workItem from="1747098953901" duration="1228000" />
      <workItem from="1747104688864" duration="37000" />
      <workItem from="1747201471042" duration="1086000" />
      <workItem from="1747635886658" duration="608000" />
      <workItem from="1748309129553" duration="300000" />
      <workItem from="1749104610865" duration="1296000" />
      <workItem from="1749195573599" duration="42000" />
      <workItem from="1749202590773" duration="657000" />
      <workItem from="1751268248392" duration="6000" />
      <workItem from="1752114522280" duration="9888000" />
      <workItem from="1752828757910" duration="4290000" />
    </task>
    <task id="LOCAL-00159" summary="flower接入监控平台异步任务监测&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28240&#10;市场项目编号（名称）:">
      <created>1725587745547</created>
      <option name="number" value="00159" />
      <option name="presentableId" value="LOCAL-00159" />
      <option name="project" value="LOCAL" />
      <updated>1725587745548</updated>
    </task>
    <task id="LOCAL-00160" summary="监控平台数据迁移功能修改&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28240&#10;市场项目编号（名称）:">
      <created>1725931265046</created>
      <option name="number" value="00160" />
      <option name="presentableId" value="LOCAL-00160" />
      <option name="project" value="LOCAL" />
      <updated>1725931265048</updated>
    </task>
    <task id="LOCAL-00161" summary="监控平台数据迁移功能修改(解压)&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28240&#10;市场项目编号（名称）:">
      <created>1725931722301</created>
      <option name="number" value="00161" />
      <option name="presentableId" value="LOCAL-00161" />
      <option name="project" value="LOCAL" />
      <updated>1725931722301</updated>
    </task>
    <task id="LOCAL-00162" summary="补充base监控单元测试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28463&#10;市场项目编号（名称）:">
      <created>1726622938210</created>
      <option name="number" value="00162" />
      <option name="presentableId" value="LOCAL-00162" />
      <option name="project" value="LOCAL" />
      <updated>1726622938211</updated>
    </task>
    <task id="LOCAL-00163" summary="补充base监控单元测试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28463&#10;市场项目编号（名称）:">
      <created>1726623595048</created>
      <option name="number" value="00163" />
      <option name="presentableId" value="LOCAL-00163" />
      <option name="project" value="LOCAL" />
      <updated>1726623595048</updated>
    </task>
    <task id="LOCAL-00164" summary="补充插件审核管理单元测试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28463&#10;市场项目编号（名称）:">
      <created>1726624073910</created>
      <option name="number" value="00164" />
      <option name="presentableId" value="LOCAL-00164" />
      <option name="project" value="LOCAL" />
      <updated>1726624073910</updated>
    </task>
    <task id="LOCAL-00165" summary="采集数据增量迁移-升级功能&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28484&#10;市场项目编号（名称）:">
      <created>1726717158397</created>
      <option name="number" value="00165" />
      <option name="presentableId" value="LOCAL-00165" />
      <option name="project" value="LOCAL" />
      <updated>1726717158398</updated>
    </task>
    <task id="LOCAL-00166" summary="采集数据增量迁移-密码加密&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28484&#10;市场项目编号（名称）:">
      <created>1726821974714</created>
      <option name="number" value="00166" />
      <option name="presentableId" value="LOCAL-00166" />
      <option name="project" value="LOCAL" />
      <updated>1726821974719</updated>
    </task>
    <task id="LOCAL-00167" summary="采集可视化展示bug修复&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28564&#10;市场项目编号（名称）:">
      <created>1727140868072</created>
      <option name="number" value="00167" />
      <option name="presentableId" value="LOCAL-00167" />
      <option name="project" value="LOCAL" />
      <updated>1727140868074</updated>
    </task>
    <task id="LOCAL-00168" summary="采集增量数据迁移&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28564&#10;市场项目编号（名称）:">
      <created>1727140893555</created>
      <option name="number" value="00168" />
      <option name="presentableId" value="LOCAL-00168" />
      <option name="project" value="LOCAL" />
      <updated>1727140893556</updated>
    </task>
    <task id="LOCAL-00169" summary="漏洞修复&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28564&#10;市场项目编号（名称）:">
      <created>1729476300650</created>
      <option name="number" value="00169" />
      <option name="presentableId" value="LOCAL-00169" />
      <option name="project" value="LOCAL" />
      <updated>1729476300652</updated>
    </task>
    <task id="LOCAL-00170" summary="漏洞修复&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-28564&#10;市场项目编号（名称）:">
      <created>1729477086353</created>
      <option name="number" value="00170" />
      <option name="presentableId" value="LOCAL-00170" />
      <option name="project" value="LOCAL" />
      <updated>1729477086354</updated>
    </task>
    <task id="LOCAL-00171" summary="数据迁移采集失败重试脚本开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-29131&#10;市场项目编号（名称）:">
      <created>1729492165089</created>
      <option name="number" value="00171" />
      <option name="presentableId" value="LOCAL-00171" />
      <option name="project" value="LOCAL" />
      <updated>1729492165090</updated>
    </task>
    <task id="LOCAL-00172" summary="review修改&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-29131&#10;市场项目编号（名称）:">
      <created>1729495380959</created>
      <option name="number" value="00172" />
      <option name="presentableId" value="LOCAL-00172" />
      <option name="project" value="LOCAL" />
      <updated>1729495380960</updated>
    </task>
    <task id="LOCAL-00173" summary="更新定时任务&#10;&#10;Code Source From: Self Code&#10;Description: 更新统计数据定时任务为10分钟，更新base采集更新部署中采集状态定时任务为10分钟&#10;Jira: #OPS-29169&#10;市场项目编号（名称）:">
      <created>1729567448689</created>
      <option name="number" value="00173" />
      <option name="presentableId" value="LOCAL-00173" />
      <option name="project" value="LOCAL" />
      <updated>1729567448692</updated>
    </task>
    <task id="LOCAL-00174" summary="检测进程端口代码删除&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29266&#10;市场项目编号（名称）:">
      <created>1729665562711</created>
      <option name="number" value="00174" />
      <option name="presentableId" value="LOCAL-00174" />
      <option name="project" value="LOCAL" />
      <updated>1729665562714</updated>
    </task>
    <task id="LOCAL-00175" summary="采集单元测试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29266&#10;市场项目编号（名称）:">
      <created>1729665792245</created>
      <option name="number" value="00175" />
      <option name="presentableId" value="LOCAL-00175" />
      <option name="project" value="LOCAL" />
      <updated>1729665792245</updated>
    </task>
    <task id="LOCAL-00176" summary="插件审核bug修复&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29281&#10;市场项目编号（名称）:">
      <created>1729675216052</created>
      <option name="number" value="00176" />
      <option name="presentableId" value="LOCAL-00176" />
      <option name="project" value="LOCAL" />
      <updated>1729675216053</updated>
    </task>
    <task id="LOCAL-00177" summary="overview功能单元测试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29356&#10;市场项目编号（名称）:">
      <created>1730082555525</created>
      <option name="number" value="00177" />
      <option name="presentableId" value="LOCAL-00177" />
      <option name="project" value="LOCAL" />
      <updated>1730082555526</updated>
    </task>
    <task id="LOCAL-00178" summary="overview功能单元测试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29356&#10;市场项目编号（名称）:">
      <created>1730082630440</created>
      <option name="number" value="00178" />
      <option name="presentableId" value="LOCAL-00178" />
      <option name="project" value="LOCAL" />
      <updated>1730082630441</updated>
    </task>
    <task id="LOCAL-00179" summary="review修改&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29356&#10;市场项目编号（名称）:">
      <created>1730104231669</created>
      <option name="number" value="00179" />
      <option name="presentableId" value="LOCAL-00179" />
      <option name="project" value="LOCAL" />
      <updated>1730104231669</updated>
    </task>
    <task id="LOCAL-00180" summary="采集任务操作记录更新优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29743&#10;市场项目编号（名称）:">
      <created>1731295231849</created>
      <option name="number" value="00180" />
      <option name="presentableId" value="LOCAL-00180" />
      <option name="project" value="LOCAL" />
      <updated>1731295231850</updated>
    </task>
    <task id="LOCAL-00181" summary="监控平台同步CMDB周期优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29797&#10;市场项目编号（名称）:">
      <created>1731376389270</created>
      <option name="number" value="00181" />
      <option name="presentableId" value="LOCAL-00181" />
      <option name="project" value="LOCAL" />
      <updated>1731376389272</updated>
    </task>
    <task id="LOCAL-00182" summary="拨测页面响应优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29808&#10;市场项目编号（名称）:">
      <created>1731389890906</created>
      <option name="number" value="00182" />
      <option name="presentableId" value="LOCAL-00182" />
      <option name="project" value="LOCAL" />
      <updated>1731389890906</updated>
    </task>
    <task id="LOCAL-00183" summary="采集任务可视化视图展示周期优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29866&#10;市场项目编号（名称）:">
      <created>1731465023905</created>
      <option name="number" value="00183" />
      <option name="presentableId" value="LOCAL-00183" />
      <option name="project" value="LOCAL" />
      <updated>1731465023914</updated>
    </task>
    <task id="LOCAL-00184" summary="review修改&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29866&#10;市场项目编号（名称）:">
      <created>1731467354301</created>
      <option name="number" value="00184" />
      <option name="presentableId" value="LOCAL-00184" />
      <option name="project" value="LOCAL" />
      <updated>1731467354303</updated>
    </task>
    <task id="LOCAL-00185" summary="CI流水线编译任务优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29880&#10;市场项目编号（名称）:">
      <created>1731554181175</created>
      <option name="number" value="00185" />
      <option name="presentableId" value="LOCAL-00185" />
      <option name="project" value="LOCAL" />
      <updated>1731554181177</updated>
    </task>
    <task id="LOCAL-00186" summary="CI流水线编译任务优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29880&#10;市场项目编号（名称）:">
      <created>1731634034015</created>
      <option name="number" value="00186" />
      <option name="presentableId" value="LOCAL-00186" />
      <option name="project" value="LOCAL" />
      <updated>1731634034016</updated>
    </task>
    <task id="LOCAL-00187" summary="监控平台同步CMDB周期优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29797&#10;市场项目编号（名称）:">
      <created>1731893795607</created>
      <option name="number" value="00187" />
      <option name="presentableId" value="LOCAL-00187" />
      <option name="project" value="LOCAL" />
      <updated>1731893795609</updated>
    </task>
    <task id="LOCAL-00188" summary="监控平台同步CMDB周期优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29797&#10;市场项目编号（名称）:">
      <created>1731894018011</created>
      <option name="number" value="00188" />
      <option name="presentableId" value="LOCAL-00188" />
      <option name="project" value="LOCAL" />
      <updated>1731894018011</updated>
    </task>
    <task id="LOCAL-00189" summary="kafka客户端由SimpleConsumer改为KafkaConsumer功能优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29985&#10;市场项目编号（名称）:">
      <created>1731997099509</created>
      <option name="number" value="00189" />
      <option name="presentableId" value="LOCAL-00189" />
      <option name="project" value="LOCAL" />
      <updated>1731997099511</updated>
    </task>
    <task id="LOCAL-00190" summary="服务拨测优化-请求节点管理失败状态置为FAILED&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29985&#10;市场项目编号（名称）:">
      <created>1732242251185</created>
      <option name="number" value="00190" />
      <option name="presentableId" value="LOCAL-00190" />
      <option name="project" value="LOCAL" />
      <updated>1732242251188</updated>
    </task>
    <task id="LOCAL-00191" summary="kafka客户端更换优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30173&#10;市场项目编号（名称）:">
      <created>1732592278954</created>
      <option name="number" value="00191" />
      <option name="presentableId" value="LOCAL-00191" />
      <option name="project" value="LOCAL" />
      <updated>1732592278955</updated>
    </task>
    <task id="LOCAL-00192" summary="kafka客户端更换优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30173&#10;市场项目编号（名称）:">
      <created>1732605215190</created>
      <option name="number" value="00192" />
      <option name="presentableId" value="LOCAL-00192" />
      <option name="project" value="LOCAL" />
      <updated>1732605215190</updated>
    </task>
    <task id="LOCAL-00193" summary="kafka客户端更换优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30173&#10;市场项目编号（名称）:">
      <created>1732605411587</created>
      <option name="number" value="00193" />
      <option name="presentableId" value="LOCAL-00193" />
      <option name="project" value="LOCAL" />
      <updated>1732605411587</updated>
    </task>
    <task id="LOCAL-00194" summary="kafka客户端更换优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30173&#10;市场项目编号（名称）:">
      <created>1732688387271</created>
      <option name="number" value="00194" />
      <option name="presentableId" value="LOCAL-00194" />
      <option name="project" value="LOCAL" />
      <updated>1732688387272</updated>
    </task>
    <task id="LOCAL-00195" summary="kafka客户端更换测试环境调试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:">
      <created>1733217814008</created>
      <option name="number" value="00195" />
      <option name="presentableId" value="LOCAL-00195" />
      <option name="project" value="LOCAL" />
      <updated>1733217814009</updated>
    </task>
    <task id="LOCAL-00196" summary="kafka客户端更换测试环境调试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:">
      <created>1733277431111</created>
      <option name="number" value="00196" />
      <option name="presentableId" value="LOCAL-00196" />
      <option name="project" value="LOCAL" />
      <updated>1733277431113</updated>
    </task>
    <task id="LOCAL-00197" summary="kafka客户端更换测试环境调试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:">
      <created>1733282295771</created>
      <option name="number" value="00197" />
      <option name="presentableId" value="LOCAL-00197" />
      <option name="project" value="LOCAL" />
      <updated>1733282295771</updated>
    </task>
    <task id="LOCAL-00198" summary="kafka客户端更换测试环境调试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:">
      <created>1733303120950</created>
      <option name="number" value="00198" />
      <option name="presentableId" value="LOCAL-00198" />
      <option name="project" value="LOCAL" />
      <updated>1733303120952</updated>
    </task>
    <task id="LOCAL-00199" summary="take增加timeout&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:">
      <created>1733304377439</created>
      <option name="number" value="00199" />
      <option name="presentableId" value="LOCAL-00199" />
      <option name="project" value="LOCAL" />
      <updated>1733304377440</updated>
    </task>
    <task id="LOCAL-00200" summary="获取采集实例状态功能开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30833&#10;市场项目编号（名称）:">
      <created>1733902600082</created>
      <option name="number" value="00200" />
      <option name="presentableId" value="LOCAL-00200" />
      <option name="project" value="LOCAL" />
      <updated>1733902600083</updated>
    </task>
    <task id="LOCAL-00201" summary="获取采集实例状态功能开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30833&#10;市场项目编号（名称）:">
      <created>1733902897992</created>
      <option name="number" value="00201" />
      <option name="presentableId" value="LOCAL-00201" />
      <option name="project" value="LOCAL" />
      <updated>1733902897993</updated>
    </task>
    <task id="LOCAL-00202" summary="获取采集实例状态分页功能开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30834&#10;市场项目编号（名称）:">
      <created>1733906013832</created>
      <option name="number" value="00202" />
      <option name="presentableId" value="LOCAL-00202" />
      <option name="project" value="LOCAL" />
      <updated>1733906013833</updated>
    </task>
    <task id="LOCAL-00203" summary="采集状态滚动下拉&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30889&#10;市场项目编号（名称）:">
      <created>1733971338285</created>
      <option name="number" value="00203" />
      <option name="presentableId" value="LOCAL-00203" />
      <option name="project" value="LOCAL" />
      <updated>1733971338290</updated>
    </task>
    <task id="LOCAL-00204" summary="采集批量重试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30892&#10;市场项目编号（名称）:">
      <created>1733972399785</created>
      <option name="number" value="00204" />
      <option name="presentableId" value="LOCAL-00204" />
      <option name="project" value="LOCAL" />
      <updated>1733972399785</updated>
    </task>
    <task id="LOCAL-00205" summary="监控平台单元测试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-33601&#10;市场项目编号（名称）:">
      <created>1747099562006</created>
      <option name="number" value="00205" />
      <option name="presentableId" value="LOCAL-00205" />
      <option name="project" value="LOCAL" />
      <updated>1747099562007</updated>
    </task>
    <task id="LOCAL-00206" summary="环境变量修改&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-33601&#10;市场项目编号（名称）:">
      <created>1747201519068</created>
      <option name="number" value="00206" />
      <option name="presentableId" value="LOCAL-00206" />
      <option name="project" value="LOCAL" />
      <updated>1747201519070</updated>
    </task>
    <task id="LOCAL-00207" summary="主机监控开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-34628&#10;市场项目编号（名称）:">
      <created>1752829540570</created>
      <option name="number" value="00207" />
      <option name="presentableId" value="LOCAL-00207" />
      <option name="project" value="LOCAL" />
      <updated>1752829540571</updated>
    </task>
    <option name="localTasksCounter" value="208" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="remote/release_bcops_deve" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="72" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="148" />
                  <entry key="Table.Space.CommitStatus.ColumnIdWidth" value="15" />
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="c6233a44-44f8-4687-9c66-98a3bc54f1a5">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="user">
                    <value>
                      <list>
                        <option value="*" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="develop_0624" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/release_bcops_2024.4" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/release_bcops_2024.3" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="review修改&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-29131&#10;市场项目编号（名称）:" />
    <MESSAGE value="更新定时任务&#10;&#10;Code Source From: Self Code&#10;Description: 更新统计数据定时任务为10分钟，更新base采集更新部署中采集状态定时任务为10分钟&#10;Jira: #OPS-29169&#10;市场项目编号（名称）:" />
    <MESSAGE value="检测进程端口代码删除&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29266&#10;市场项目编号（名称）:" />
    <MESSAGE value="采集单元测试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29266&#10;市场项目编号（名称）:" />
    <MESSAGE value="插件审核bug修复&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29281&#10;市场项目编号（名称）:" />
    <MESSAGE value="overview功能单元测试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29356&#10;市场项目编号（名称）:" />
    <MESSAGE value="review修改&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29356&#10;市场项目编号（名称）:" />
    <MESSAGE value="采集任务操作记录更新优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29743&#10;市场项目编号（名称）:" />
    <MESSAGE value="拨测页面响应优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29808&#10;市场项目编号（名称）:" />
    <MESSAGE value="采集任务可视化视图展示周期优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29866&#10;市场项目编号（名称）:" />
    <MESSAGE value="review修改&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29866&#10;市场项目编号（名称）:" />
    <MESSAGE value="CI流水线编译任务优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29880&#10;市场项目编号（名称）:" />
    <MESSAGE value="监控平台同步CMDB周期优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29797&#10;市场项目编号（名称）:" />
    <MESSAGE value="kafka客户端由SimpleConsumer改为KafkaConsumer功能优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29985&#10;市场项目编号（名称）:" />
    <MESSAGE value="服务拨测优化-请求节点管理失败状态置为FAILED&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-29985&#10;市场项目编号（名称）:" />
    <MESSAGE value="kafka客户端更换优化&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30173&#10;市场项目编号（名称）:" />
    <MESSAGE value="kafka客户端更换测试环境调试&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:" />
    <MESSAGE value="take增加timeout&#10;&#10;Code Source From: Self Code&#10;Description: &#10;Jira: #OPS-30303&#10;市场项目编号（名称）:" />
    <MESSAGE value="获取采集实例状态功能开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30833&#10;市场项目编号（名称）:" />
    <MESSAGE value="获取采集实例状态分页功能开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30834&#10;市场项目编号（名称）:" />
    <MESSAGE value="采集状态滚动下拉&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30889&#10;市场项目编号（名称）:" />
    <MESSAGE value="采集批量重试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-30892&#10;市场项目编号（名称）:" />
    <MESSAGE value="监控平台单元测试&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-33601&#10;市场项目编号（名称）:" />
    <MESSAGE value="环境变量修改&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-33601&#10;市场项目编号（名称）:" />
    <MESSAGE value="主机监控开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-34628&#10;市场项目编号（名称）:" />
    <option name="LAST_COMMIT_MESSAGE" value="主机监控开发&#10;&#10;Code Source From: Self Code&#10;Description:&#10;Jira: #OPS-34628&#10;市场项目编号（名称）:" />
    <option name="GROUP_MULTIFILE_MERGE_BY_DIRECTORY" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/bkmonitor/strategy/new_strategy.py</url>
          <line>1721</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/bkmonitor/strategy/new_strategy.py</url>
          <line>1723</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/collect_process/upgrade.py</url>
          <line>84</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/collect_process/zhengli.py</url>
          <line>42</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py</url>
          <line>2335</line>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/monitor_venv/lib/python3.6/site-packages/kafka/protocol/parser.py</url>
          <line>65</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py</url>
          <line>2047</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py</url>
          <line>2040</line>
          <option name="timeStamp" value="37" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py</url>
          <line>1970</line>
          <option name="timeStamp" value="38" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py</url>
          <line>2030</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/collecting/resources/backend_resources.py</url>
          <line>3244</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/views.py</url>
          <line>185</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/views.py</url>
          <line>161</line>
          <option name="timeStamp" value="45" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/views.py</url>
          <line>125</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/views.py</url>
          <line>154</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/bkmonitor/packages/monitor_web/plugin/views.py</url>
          <line>145</line>
          <option name="timeStamp" value="48" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/bk_monitor$collect_process.coverage" NAME="collect_process Coverage Results" MODIFIED="1726045083261" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$delete_collectors.coverage" NAME="delete_collectors Coverage Results" MODIFIED="1718768618170" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$manage.coverage" NAME="manage Coverage Results" MODIFIED="1733974690340" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bkmonitor" />
    <SUITE FILE_PATH="coverage/bk_monitor$deploy_collect.coverage" NAME="deploy_collect Coverage Results" MODIFIED="1718608494365" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$test.coverage" NAME="test Coverage Results" MODIFIED="1709535462901" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/bk_monitor$python_manage_py_run_access__s_access___access_type_data___min_interval_30.coverage" NAME="python manage.py run_access -s access --access-type=data --min-interval 30 Coverage Results" MODIFIED="1719299695092" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/bkmonitor/alarm_backends" />
    <SUITE FILE_PATH="coverage/bk_monitor$status_data.coverage" NAME="status_data Coverage Results" MODIFIED="1725413102905" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process/status" />
    <SUITE FILE_PATH="coverage/bk_monitor$duibi.coverage" NAME="duibi Coverage Results" MODIFIED="1726134693841" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$sre_parse.coverage" NAME="sre_parse Coverage Results" MODIFIED="1709533895118" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="/Library/Frameworks/Python.framework/Versions/3.6/lib/python3.6" />
    <SUITE FILE_PATH="coverage/bk_monitor$toggle_collectors.coverage" NAME="toggle_collectors Coverage Results" MODIFIED="1718768322045" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$config.coverage" NAME="config Coverage Results" MODIFIED="1718768317881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$upgrade.coverage" NAME="upgrade Coverage Results" MODIFIED="1726709870580" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$address_name.coverage" NAME="address_name Coverage Results" MODIFIED="1719565337164" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
    <SUITE FILE_PATH="coverage/bk_monitor$encrypt.coverage" NAME="encrypt Coverage Results" MODIFIED="1726737414736" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/collect_process" />
  </component>
</project>